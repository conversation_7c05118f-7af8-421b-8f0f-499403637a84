<form wire:submit="loadTalkdeskData" class="space-y-4">
    <div class="flex flex-col sm:flex-row gap-4">
        <!-- Type Selection -->
        <div class="w-full sm:w-2/3">
            <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Call Data Type</label>
            <select wire:model="typeForm.type" id="type" class="input">
                <option value="daily">Daily</option>
                <option value="monthly">Monthly</option>
            </select>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['typeForm.type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-xs"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end mt-4">
        <button type="submit" class="btn btn-primary">Load Data</button>
    </div>
    <div>
        <div wire:loading wire:target="loadTalkdeskData" class="mt-2">
            <div class="flex items-center space-x-2">
                <i wire:loading class="ki-filled ki-loading animate-spin"></i>
                <span>Loading...</span>
            </div>
        </div>
    </div>
</form><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/crm/poms-talkdesk/type-form.blade.php ENDPATH**/ ?>