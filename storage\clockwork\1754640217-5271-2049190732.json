{"id": "1754640217-5271-2049190732", "version": 1, "type": "request", "time": 1754640217.220789, "method": "POST", "url": "http://localhost:8000/livewire/update", "uri": "/livewire/update", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "content-length": ["1201"], "sec-ch-ua-platform": ["\"Windows\""], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""], "content-type": ["application/json"], "x-livewire": [""], "sec-ch-ua-mobile": ["?0"], "accept": ["*/*"], "origin": ["http://localhost:8000"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["cors"], "sec-fetch-dest": ["empty"], "referer": ["http://localhost:8000/ep?tab=tracking-diary"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.1747301190; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=1751960299; sugar_user_theme=SuiteP; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0%3D; snipeit_session=fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm; optional_user_info_open=true; snipeit_passport_token=eyJpdiI6IlQrMExxNDVtYWlGQlJ4YTRzcEQwZnc9PSIsInZhbHVlIjoiclo3MFpxRGlCbWhxRmJPOGhWQzNRYTN5Rlk5VmNlZDY2Sm1wczgxRFRzajI0SVBIaGhzQWs0aDBjYWFubndTMXJDUkd4YUZLbFFFMUo0NmZSa3pWRkl6SnJDOW1Hc2w1dXY2dzE3QTUrV1owVVlnU01IS0pIeDdyOGhtTjVtTjZCLzlNSmVYZE1LRG13amdJOC9MRU9EdlRIdit5ZmlZVm8wckJLZE5mVTJhWGYzMGFKVlZvemZodHhaS2lvb3BKZXQ3SzNEcnVBazFBNjZLMGhjWDlubWZacng1NFk0TjBGc0xBc3VhUno5N0hrRzAzbi9jNnE1SXY3K1Z1UlAwdk1RMXVjOThKWWZ4UDEwMzI3YVhRbG1pSUVNZUZ3R2wxdUg0amdMYzcvRFNMaGVWNG5ub3pyeWZnNy9mczI3cXEiLCJtYWMiOiI1NDg4NTEyMTU4NDc4NGNhNDU4Mjg4MGYxOWEwNzUxMDJiMjQxNjJhNzY4OTI0NzVkNzc0NmEyZWQ2NTgyMmEyIiwidGFnIjoiIn0%3D; optional_info_open=false; ep_kontrak_session=eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImY3N2hxdEZSL0RwSWszd0Y2cmJlMVE9PSIsInZhbHVlIjoiWjdMVU8xN2UwRmhzQzI0YUloNnZaK0hUQ1ZSbmVITG1BWTNLTEJSSVFOdlZ5eTUvK1NvZ3hSUzVTckFFS0Vqelo0SnBYZGF4OEp6ZTM4QW5JWENOcjhSNkVFcUlEWVhKRWFMY2FoYmpZOHk2YWZ1Y091RTZLcm5UWndqYWZsd2ciLCJtYWMiOiIxZjJkNmNjOTZlM2E0NmEzNzdlZDNhZWVmOWQ4OGY2YTlhNTZiOGI3NmIzZjM2ZDQ1YzVlNjI5YTNlZmU3YzI4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjVUV292WWNsdEplQlJEQnlqL0FVaWc9PSIsInZhbHVlIjoia3hxVmdGc090b0FKMklJZ3ZoWUZGN04rOHQwM0tDQlFUdUdLZWFiQ1VJOHRxcmo3QjVnb2VnZDNSZi9QMVZmWS9HK2pHV3o4OFB6RXc0KzRRcTBGUUpuYnhaalczYlo1ODQ4dk1ZYXdHYnNyN05JVTlWR3JURXlaNnBxYVVydTAiLCJtYWMiOiI0YmJkN2ZjYjUxMGRhNWVhMDU4NjU5NmM4MjYyYzY1NGNjMjlmNWU4Njk3MDU2YjM2YTQ4MDAzNjRjOTA0N2U5IiwidGFnIjoiIn0%3D; x-clockwork=%7B%22requestId%22%3A%221754640211-9334-*********%22%2C%22version%22%3A%225.3.4%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%22711f8eab%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "getData": [], "postData": [], "requestData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "components": {"__type__": "array", "0": {"__type__": "array", "snapshot": "{\"data\":{\"trackingSearchForm\":[{\"documentNumber\":\"\"},{\"class\":\"App\\\\Livewire\\\\Forms\\\\Fulfilment\\\\TrackingDiarySearchForm\",\"s\":\"form\"}],\"listdata\":[[],{\"s\":\"arr\"}],\"allTrackingData\":[[],{\"s\":\"arr\"}],\"supplier\":null,\"smAppl\":null,\"preparedPtj\":null,\"issuedPtj\":null,\"createdPtj\":null,\"chargePtj\":null,\"poco_no\":null,\"sqInfo\":null,\"carian\":null,\"errorMessage\":null,\"isSearching\":false,\"isModalOpen\":false,\"modalTitle\":\"\",\"modalType\":\"\",\"modalDocNo\":\"\",\"listDataWorkflow\":[[],{\"s\":\"arr\"}],\"listDataDoFn\":[[],{\"s\":\"arr\"}],\"listDataYepMenu\":[[],{\"s\":\"arr\"}],\"modalData\":[[],{\"s\":\"arr\"}],\"isLoading\":false},\"memo\":{\"id\":\"pirQegaUpEaH0FhPAHdV\",\"name\":\"module.fulfilment.tracking-diary\",\"path\":\"ep\",\"method\":\"GET\",\"children\":[],\"scripts\":[\"3791712019-0\"],\"assets\":[],\"errors\":[],\"locale\":\"en\"},\"checksum\":\"4c694fd08f44764d28c9984a67eb955e2603955fdfb616e476d70e348be82a60\"}", "updates": {"__type__": "array", "trackingSearchForm.documentNumber": "*****************"}, "calls": {"__type__": "array", "0": {"__type__": "array", "path": "", "method": "search", "params": {"__type__": "array"}}}}}}, "sessionData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "_previous": {"__type__": "array", "url": "http://localhost:8000/ep?tab=tracking-diary"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 4}, "authenticatedUser": {"id": 4, "username": "<EMAIL>", "email": "<EMAIL>", "name": "IQBAL FIKRI MOHAMED MISMAN ."}, "cookies": {"_ga": "GA1.1.913056526.1747301190", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "1751960299", "sugar_user_theme": "SuiteP", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0=", "snipeit_session": "fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm", "optional_user_info_open": "true", "snipeit_passport_token": "*removed*", "optional_info_open": "false", "ep_kontrak_session": "eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6ImY3N2hxdEZSL0RwSWszd0Y2cmJlMVE9PSIsInZhbHVlIjoiWjdMVU8xN2UwRmhzQzI0YUloNnZaK0hUQ1ZSbmVITG1BWTNLTEJSSVFOdlZ5eTUvK1NvZ3hSUzVTckFFS0Vqelo0SnBYZGF4OEp6ZTM4QW5JWENOcjhSNkVFcUlEWVhKRWFMY2FoYmpZOHk2YWZ1Y091RTZLcm5UWndqYWZsd2ciLCJtYWMiOiIxZjJkNmNjOTZlM2E0NmEzNzdlZDNhZWVmOWQ4OGY2YTlhNTZiOGI3NmIzZjM2ZDQ1YzVlNjI5YTNlZmU3YzI4IiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6IjVUV292WWNsdEplQlJEQnlqL0FVaWc9PSIsInZhbHVlIjoia3hxVmdGc090b0FKMklJZ3ZoWUZGN04rOHQwM0tDQlFUdUdLZWFiQ1VJOHRxcmo3QjVnb2VnZDNSZi9QMVZmWS9HK2pHV3o4OFB6RXc0KzRRcTBGUUpuYnhaalczYlo1ODQ4dk1ZYXdHYnNyN05JVTlWR3JURXlaNnBxYVVydTAiLCJtYWMiOiI0YmJkN2ZjYjUxMGRhNWVhMDU4NjU5NmM4MjYyYzY1NGNjMjlmNWU4Njk3MDU2YjM2YTQ4MDAzNjRjOTA0N2U5IiwidGFnIjoiIn0=", "x-clockwork": "{\"requestId\":\"1754640211-9334-*********\",\"version\":\"5.3.4\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"711f8eab\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": **********.956608, "responseStatus": 200, "responseDuration": 1735.8191013336182, "memoryUsage": 31457280, "middleware": ["web"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB' LIMIT 1", "duration": 57.16, "connection": "epss2", "time": 1754640217.8039181, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 47, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 4 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 9.14, "connection": "epss2", "time": 1754640217.903039, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->then()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Drawer\\Utils.php", "line": 182, "isVendor": true}, {"call": "Livewire\\Drawer\\Utils::applyMiddleware()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php", "line": 98, "isVendor": true}, {"call": "Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->applyPersistentMiddleware()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware.php", "line": 43, "isVendor": true}, {"call": "Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware->{closure:Livewire\\Mechanisms\\PersistentMiddleware\\PersistentMiddleware::boot():37}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\EventBus.php", "line": 60, "isVendor": true}, {"call": "Livewire\\EventBus->trigger()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\helpers.php", "line": 98, "isVendor": true}, {"call": "Livewire\\trigger()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 126, "isVendor": true}, {"call": "Livewire\\Mechanisms\\HandleComponents\\HandleComponents->fromSnapshot()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 92, "isVendor": true}, {"call": "Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\LivewireManager.php", "line": 102, "isVendor": true}, {"call": "Livewire\\LivewireManager->update()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleRequests\\HandleRequests.php", "line": 94, "isVendor": true}, {"call": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "   SELECT DISTINCT \n                            A.APPL_NO AS AP_DOC_NO \n                           FROM PM_ACCESS_APPL A \n                      WHERE \n                       A.APPL_NO = ***************** \n                ", "duration": 8.13, "connection": "oracle_nextgen_rpt", "time": **********.6903808, "trace": [{"call": "Illuminate\\Database\\Connection->select()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 240, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getListDocNoMarketResearchTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 119, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}, {"call": "Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 101, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM (SELECT * FROM \"PM_TRACKING_DIARY\" WHERE \"DOC_NO\" = ***************** and \"GROUP_ID\" IS not NULL) WHERE rownum = 1", "duration": 9.69, "connection": "oracle_nextgen_rpt", "time": **********.700181, "trace": [{"call": "Illuminate\\Database\\Query\\Builder->first()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 250, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getGroupIdTrackingDiary()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 177, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}, {"call": "Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 101, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT \"TD\".*, \"SD\".\"STATUS_NAME\" FROM \"PM_TRACKING_DIARY\" td inner join \"PM_STATUS_DESC\" sd on \"TD\".\"STATUS_ID\" = \"SD\".\"STATUS_ID\" WHERE \"SD\".\"LANGUAGE_CODE\" = 'en' and \"TD\".\"DOC_NO\" = *****************", "duration": 15.44, "connection": "oracle_nextgen_rpt", "time": **********.711302, "trace": [{"call": "Illuminate\\Database\\Query\\Builder->get()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 283, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getListTrackingDiaryByDocNo()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 259, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getListTrackingDiary()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 188, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT \"TD\".*, \"SD\".\"STATUS_NAME\" FROM \"PM_TRACKING_DIARY\" td inner join \"PM_STATUS_DESC\" sd on \"TD\".\"STATUS_ID\" = \"SD\".\"STATUS_ID\" WHERE \"SD\".\"LANGUAGE_CODE\" = 'en' and \"TD\".\"GROUP_ID\" = 21533399", "duration": 31.72, "connection": "oracle_nextgen_rpt", "time": **********.727472, "trace": [{"call": "Illuminate\\Database\\Query\\Builder->get()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 294, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getListTrackingDiaryByGroupId()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\FulfilmentService.php", "line": 266, "isVendor": false}, {"call": "App\\Services\\FulfilmentService->getListTrackingDiary()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 188, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_role_role_codi_ep')", "duration": 10.47, "connection": "epss2", "time": **********.766401, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT exists(SELECT * FROM `roles` inner join `users_roles` on `roles`.`id` = `users_roles`.`role_id` WHERE `users_roles`.`user_id` = 4 and `role_code` = 'role_codi_ep' and `roles`.`deleted_at` IS NULL) as `exists`", "duration": 9.39, "connection": "epss2", "time": **********.7845008, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 91, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT exists(SELECT * FROM `groups` inner join `users_groups` on `groups`.`id` = `users_groups`.`group_id` WHERE `users_groups`.`user_id` = 4 and exists (SELECT * FROM `roles` inner join `groups_roles` on `roles`.`id` = `groups_roles`.`role_id` WHERE `groups`.`id` = `groups_roles`.`group_id` and `role_code` = 'role_codi_ep' and `roles`.`deleted_at` IS NULL) and `groups`.`deleted_at` IS NULL) as `exists`", "duration": 9.24, "connection": "epss2", "time": **********.7957592, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 101, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": "App\\Models\\Group", "tags": []}, {"query": "INSERT INTO `cache` (`expiration`, `key`, `value`) VALUES (1754726618, 'laravel_cache_user_4_role_role_codi_ep', 'b:0;') on duplicate key UPDATE `expiration` = VALUES(`expiration`), `key` = VALUES(`key`), `value` = VALUES(`value`)", "duration": 14.97, "connection": "epss2", "time": **********.806165, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_role_super_admin')", "duration": 8.18, "connection": "epss2", "time": **********.83154, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": null, "tags": []}, {"query": "SELECT exists(SELECT * FROM `roles` inner join `users_roles` on `roles`.`id` = `users_roles`.`role_id` WHERE `users_roles`.`user_id` = 4 and `role_code` = 'super_admin' and `roles`.`deleted_at` IS NULL) as `exists`", "duration": 9.34, "connection": "epss2", "time": **********.841512, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 91, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT exists(SELECT * FROM `groups` inner join `users_groups` on `groups`.`id` = `users_groups`.`group_id` WHERE `users_groups`.`user_id` = 4 and exists (SELECT * FROM `roles` inner join `groups_roles` on `roles`.`id` = `groups_roles`.`role_id` WHERE `groups`.`id` = `groups_roles`.`group_id` and `role_code` = 'super_admin' and `roles`.`deleted_at` IS NULL) and `groups`.`deleted_at` IS NULL) as `exists`", "duration": 8.88, "connection": "epss2", "time": **********.8528452, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 101, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": "App\\Models\\Group", "tags": []}, {"query": "INSERT INTO `cache` (`expiration`, `key`, `value`) VALUES (1754726618, 'laravel_cache_user_4_role_super_admin', 'b:0;') on duplicate key UPDATE `expiration` = VALUES(`expiration`), `key` = VALUES(`key`), `value` = VALUES(`value`)", "duration": 10.4, "connection": "epss2", "time": **********.8624768, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_role_role_adv_ep')", "duration": 9.38, "connection": "epss2", "time": **********.8741, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": null, "tags": []}, {"query": "SELECT exists(SELECT * FROM `roles` inner join `users_roles` on `roles`.`id` = `users_roles`.`role_id` WHERE `users_roles`.`user_id` = 4 and `role_code` = 'role_adv_ep' and `roles`.`deleted_at` IS NULL) as `exists`", "duration": 9.03, "connection": "epss2", "time": **********.8857138, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 91, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": "App\\Models\\Role", "tags": []}, {"query": "SELECT exists(SELECT * FROM `groups` inner join `users_groups` on `groups`.`id` = `users_groups`.`group_id` WHERE `users_groups`.`user_id` = 4 and exists (SELECT * FROM `roles` inner join `groups_roles` on `roles`.`id` = `groups_roles`.`role_id` WHERE `groups`.`id` = `groups_roles`.`group_id` and `role_code` = 'role_adv_ep' and `roles`.`deleted_at` IS NULL) and `groups`.`deleted_at` IS NULL) as `exists`", "duration": 9.05, "connection": "epss2", "time": **********.896415, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 101, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": "App\\Models\\Group", "tags": []}, {"query": "INSERT INTO `cache` (`expiration`, `key`, `value`) VALUES (1754726618, 'laravel_cache_user_4_role_role_adv_ep', 'b:1;') on duplicate key UPDATE `expiration` = VALUES(`expiration`), `key` = VALUES(`key`), `value` = VALUES(`value`)", "duration": 17.17, "connection": "epss2", "time": **********.906599, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoid2dBM1dyMlFMeklWUWgzRzFpYXU1aFpZZmVmeENpRmVpbGtrTHA3NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDM6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9lcD90YWI9dHJhY2tpbmctZGlhcnkiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', `last_activity` = **********, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB'", "duration": 11.14, "connection": "epss2", "time": **********.9445028, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 47, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 109, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 19, "databaseSlowQueries": 0, "databaseSelects": 15, "databaseInserts": 3, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 267.92, "cacheQueries": [{"type": "miss", "key": "user_4_role_role_codi_ep", "expiration": null, "time": **********.777659, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}]}, {"type": "write", "key": "user_4_role_role_codi_ep", "expiration": 86400, "time": **********.82194, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 66, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->hasCodiRole()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 211, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->getListDocNoTracking()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Module\\Fulfilment\\TrackingDiary.php", "line": 268, "isVendor": false}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->search()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 474, "isVendor": true}]}, {"type": "miss", "key": "user_4_role_super_admin", "expiration": null, "time": **********.840299, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}, {"type": "write", "key": "user_4_role_super_admin", "expiration": 86400, "time": **********.873174, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}, {"type": "miss", "key": "user_4_role_role_adv_ep", "expiration": null, "time": **********.884171, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 86, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}, {"type": "write", "key": "user_4_role_role_adv_ep", "expiration": 86400, "time": **********.924333, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 105, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}], "cacheReads": 3, "cacheHits": 0, "cacheWrites": 3, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754640217.764565, "end": **********.956367, "duration": 1191.8020248413086, "color": null, "data": null}], "log": [{"message": "N+1 queries: App\\Models\\User::App\\Models\\Role loaded 2 times.", "exception": null, "context": {"__type__": "array", "performance": true}, "level": "warning", "time": **********.957424, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 91, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}, {"message": "N+1 queries: App\\Models\\User::App\\Models\\Group loaded 2 times.", "exception": null, "context": {"__type__": "array", "performance": true}, "level": "warning", "time": **********.957517, "trace": [{"call": "Illuminate\\Database\\Eloquent\\Relations\\Relation->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 101, "isVendor": false}, {"call": "App\\Models\\User->hasRole()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\9c826de2e938206ec18ec612b399d047.php", "line": 3, "isVendor": false}, {"call": "include()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 37, "isVendor": true}, {"call": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74, "isVendor": true}, {"call": "Illuminate\\View\\Engines\\CompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16, "isVendor": true}, {"call": "Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\View\\View->getContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 191, "isVendor": true}, {"call": "Illuminate\\View\\View->renderContents()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160, "isVendor": true}, {"call": "Illuminate\\View\\View->render()", "file": "E:\\workspace\\epss-revamp\\storage\\framework\\views\\046e9a807f309d716fd2ca95759cc3e0.php", "line": 15, "isVendor": false}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "POST", "uri": "download", "name": "download", "action": "App\\Http\\Controllers\\ExportController@downloadUom", "middleware": ["web"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": **********.825093, "end": **********.825093, "duration": 0, "color": null, "data": {"name": "livewire.fulfilment.tracking-diary", "data": {"__type__": "array", "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__type__": "recursion"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__type__": "recursion"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": **********.826853, "end": **********.826853, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.search-form", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": **********.828615, "end": **********.828615, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.results-container", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": **********.925187, "end": **********.925187, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.result-container.technical-specialist", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": **********.928699, "end": **********.928699, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.result-container.results-table", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": **********.931466, "end": **********.931466, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.document-details", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": null}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": "PO240000000368947", "sqInfo": null, "carian": "*****************", "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": "*****************"}, "allTrackingData": {"__class__": "Illuminate\\Support\\Collection", "*items": {"__type__": "array", "0": {"__class__": "stdClass", "tracking_diary_id": "380916883", "doc_type": "IN", "doc_id": "20343840", "doc_no": "*****************", "action_desc": "Invois 369V117320  telah dicipta dan diserah oleh BORNEO MOBILITY (SABAH) SDN. BHD..", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "44005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Submitted"}, "1": {"__class__": "stdClass", "tracking_diary_id": "379286390", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah disahkan oleh <PERSON>.", "actioned_by": "40016", "actioned_date": "2024-06-10 15:53:35", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Acknowledged"}, "2": {"__class__": "stdClass", "tracking_diary_id": "379215997", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diterima oleh HASIAH BINTI ASSIM dan diserah untuk pengesahan.", "actioned_by": "848431", "actioned_date": "2024-06-10 12:05:12", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "3": {"__class__": "stdClass", "tracking_diary_id": "378182335", "doc_type": "DO", "doc_id": "21164236", "doc_no": "*****************", "action_desc": "<PERSON><PERSON><PERSON> 359R118707 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-05-31 15:01:53", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21532480", "status_name": "Pending Acknowledgement"}, "4": {"__class__": "stdClass", "tracking_diary_id": "377614287", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dicipta bagi Nota Minta RN240000000490941.", "actioned_by": "0", "actioned_date": "2024-05-28 08:25:29", "role_code": null, "status_id": "40000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Purchase Request Creation"}, "5": {"__class__": "stdClass", "tracking_diary_id": "377696323", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diserah kepada <PERSON><PERSON><PERSON> untuk kel<PERSON>an.", "actioned_by": "589286", "actioned_date": "2024-05-28 12:21:27", "role_code": "REQUISITIONER", "status_id": "40010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "6": {"__class__": "stdClass", "tracking_diary_id": "377697598", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS bagi semakan peruntukan.", "actioned_by": "777443", "actioned_date": "2024-05-28 12:25:30", "role_code": "FL_APPROVER", "status_id": "40100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "7": {"__class__": "stdClass", "tracking_diary_id": "377697615", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 menunggu status semakan chargeline dari 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-05-28 12:25:33", "role_code": null, "status_id": "40110", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending PR Review from 1GFMAS"}, "8": {"__class__": "stdClass", "tracking_diary_id": "377717543", "doc_type": "PR", "doc_id": "22681182", "doc_no": "PR240000000393507", "action_desc": "Permintaan Pembelian PR240000000393507 telah diluluskan dengan Nombor Pesanan Kerajaan PO240000000368947.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "40300", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Purchase Request Approved"}, "9": {"__class__": "stdClass", "tracking_diary_id": "377717544", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dicipta dan diserah kepada BORNEO MOBILITY (SABAH) SDN. BHD. untuk pengesahan.", "actioned_by": "0", "actioned_date": "2024-05-28 14:17:39", "role_code": null, "status_id": "41000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Supplier Acknowledgement"}, "10": {"__class__": "stdClass", "tracking_diary_id": "377828004", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah disahkan oleh BORNEO MOBILITY (SABAH) SDN. BHD. untuk Pemenuh<PERSON> dan <PERSON>.", "actioned_by": "725973", "actioned_date": "2024-05-29 09:44:58", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Fulfilment"}, "11": {"__class__": "stdClass", "tracking_diary_id": "380812162", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON>/Perkhidmatan telah diluluskan oleh <PERSON> dan menunggu penciptaan Invois bagi Pesanan Kerajaan PO240000000368947.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "41015", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Invoice"}, "12": {"__class__": "stdClass", "tracking_diary_id": "380916884", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Invois 369V117320  telah diserahkan untuk Padanan Bayaran.", "actioned_by": "725973", "actioned_date": "2024-06-24 08:33:42", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "41025", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Match"}, "13": {"__class__": "stdClass", "tracking_diary_id": "381258842", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Arahan Pembayaran PA240000000586837 sedang menung<PERSON> Bayaran dari iGFMAS.", "actioned_by": "0", "actioned_date": "2024-06-25 14:21:24", "role_code": null, "status_id": "41026", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment Instruction Query from 1GFMAS"}, "14": {"__class__": "stdClass", "tracking_diary_id": "381496620", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "<PERSON><PERSON><PERSON> PO240000000368947 telah dibuat padanan bayaran oleh <PERSON>a Binti Amat dan sedang berstatus Menunggu Bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "41030", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Payment"}, "15": {"__class__": "stdClass", "tracking_diary_id": "393878136", "doc_type": "PO", "doc_id": "21533399", "doc_no": "PO240000000368947", "action_desc": "Bayaran telah dilaksanakan di 1GFMAS dan <PERSON><PERSON><PERSON><PERSON><PERSON> dan Perkhidmatan bagi Pesanan Kerajaan PO240000000368947 telah selesai.", "actioned_by": "0", "actioned_date": "2024-09-22 21:50:20", "role_code": null, "status_id": "41035", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Closed"}, "16": {"__class__": "stdClass", "tracking_diary_id": "380200835", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah diterima o<PERSON>h <PERSON> dan diserah untuk pengesahan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "17": {"__class__": "stdClass", "tracking_diary_id": "379632107", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah diserah kepada Pegawai Penerima Barang untuk pengesahan.", "actioned_by": "725973", "actioned_date": "2024-06-12 10:53:11", "role_code": "MOF_SUPPLIER_ADMIN", "status_id": "42000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "18": {"__class__": "stdClass", "tracking_diary_id": "380812161", "doc_type": "DO", "doc_id": "21215581", "doc_no": "60005353532400225", "action_desc": "<PERSON><PERSON><PERSON> 369R115135 telah disahkan o<PERSON>.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:22", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "42005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Acknowledged"}, "19": {"__class__": "stdClass", "tracking_diary_id": "380200735", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah dicipta oleh <PERSON>.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:02", "role_code": "RECEIVING_OFFICER", "status_id": "43000", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Acknowledgement"}, "20": {"__class__": "stdClass", "tracking_diary_id": "380200834", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON>esanan <PERSON> 369R115135 telah disahkan o<PERSON>h <PERSON> dan Nota <PERSON>eri<PERSON>/Perkhidmatan FN240000000595207 telah diserah kepada Yatmi Binti Nordin untuk kelulusan.", "actioned_by": "880477", "actioned_date": "2024-06-18 09:02:43", "role_code": "RECEIVING_OFFICER", "status_id": "43002", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Pending Approval"}, "21": {"__class__": "stdClass", "tracking_diary_id": "380812163", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 telah berjaya diterima oleh 1GFMAS.", "actioned_by": "0", "actioned_date": "2024-06-21 16:42:22", "role_code": null, "status_id": "43010", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Approved"}, "22": {"__class__": "stdClass", "tracking_diary_id": "380812668", "doc_type": "FN", "doc_id": "21030630", "doc_no": "FN240000000595207", "action_desc": "<PERSON><PERSON>/Perkhidmatan FN240000000595207 dilulus<PERSON> o<PERSON>h <PERSON> dan sedang menunggu antaramuka dengan 1GFMAS.", "actioned_by": "661302", "actioned_date": "2024-06-21 16:42:14", "role_code": "ACKNOWLEDGE_OFFICER", "status_id": "43100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}, "24": {"__class__": "stdClass", "tracking_diary_id": "381496621", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "<PERSON><PERSON>em<PERSON>aran bagi Pa<PERSON> Pembayaran PA240000000586837 telah se<PERSON>ai.", "actioned_by": "0", "actioned_date": "2024-06-26 15:12:33", "role_code": null, "status_id": "46005", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Payment Instruction Completed"}, "25": {"__class__": "stdClass", "tracking_diary_id": "381260091", "doc_type": "PA", "doc_id": "20307541", "doc_no": "PA240000000586837", "action_desc": "Padanan Pembayaran PA240000000586837 telah dibuat padanan bayaran oleh Lina Binti Amat dan sedang menunggu antaramuka dengan 1GFMAS untuk arahan bayaran.", "actioned_by": "42026", "actioned_date": "2024-06-25 14:21:17", "role_code": "PAY_OFFICER", "status_id": "46100", "group_doc_type": "PO", "group_id": "21533399", "status_name": "Awaiting 1GFMAS Response"}}, "*escapeWhenCastingToString": false}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "78592b38"}