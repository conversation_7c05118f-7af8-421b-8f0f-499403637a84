{"id": "1754640211-9334-692933739", "version": 1, "type": "request", "time": 1754640211.688946, "method": "GET", "url": "http://localhost:8000/ep?tab=tracking-diary", "uri": "/ep?tab=tracking-diary", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "referer": ["http://localhost:8000/ep"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.1747301190; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=1751960299; sugar_user_theme=SuiteP; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0%3D; snipeit_session=fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm; optional_user_info_open=true; snipeit_passport_token=eyJpdiI6IlQrMExxNDVtYWlGQlJ4YTRzcEQwZnc9PSIsInZhbHVlIjoiclo3MFpxRGlCbWhxRmJPOGhWQzNRYTN5Rlk5VmNlZDY2Sm1wczgxRFRzajI0SVBIaGhzQWs0aDBjYWFubndTMXJDUkd4YUZLbFFFMUo0NmZSa3pWRkl6SnJDOW1Hc2w1dXY2dzE3QTUrV1owVVlnU01IS0pIeDdyOGhtTjVtTjZCLzlNSmVYZE1LRG13amdJOC9MRU9EdlRIdit5ZmlZVm8wckJLZE5mVTJhWGYzMGFKVlZvemZodHhaS2lvb3BKZXQ3SzNEcnVBazFBNjZLMGhjWDlubWZacng1NFk0TjBGc0xBc3VhUno5N0hrRzAzbi9jNnE1SXY3K1Z1UlAwdk1RMXVjOThKWWZ4UDEwMzI3YVhRbG1pSUVNZUZ3R2wxdUg0amdMYzcvRFNMaGVWNG5ub3pyeWZnNy9mczI3cXEiLCJtYWMiOiI1NDg4NTEyMTU4NDc4NGNhNDU4Mjg4MGYxOWEwNzUxMDJiMjQxNjJhNzY4OTI0NzVkNzc0NmEyZWQ2NTgyMmEyIiwidGFnIjoiIn0%3D; optional_info_open=false; ep_kontrak_session=eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFac05oYTBVVldnQ1VTeGF2UzJIbGc9PSIsInZhbHVlIjoiNWxBekF1ZHlLTjZRNHZuS1IyYys4bm1aR1QvQ1djdUQ0NnB3M1U3Sy82eWl2cWx5T0JFSWhha3YwZXRMUnhmYmlpdWQrMjVGNmdUckg2YjFUeGgxN1V0TnJ1eGhMcWpVZDd5QXFyUENMOGJCVDU5QW0yWnBRV0hRc3plSWltVlMiLCJtYWMiOiI3MjJiZWRhY2FjN2UyYzNmYTU3NjliYmYxZGFmNTY3MGU4YzY3NDNmM2YwMjFjNjllYmIzMmMzYjQ4ZjM5YjdjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InlQdUdVbUROMWRrMjEzWUJLMUZKVFE9PSIsInZhbHVlIjoicFV6bFNrREt6K0RIeFg2c3FBNEd4dTF2bUlQOXNPSXE2aWlJMXQzMXUzUHhSNlNPUklWUTVMOXpScjZ3MTQ4QXFZUjhFaGF6QUcrbEZmVnNUNlh5TDdlMXJiUkNVVUFZV3dQZUxPMGVraVlUcnRFU3g1T3lBVXlsYkZKOG5YZjQiLCJtYWMiOiIwZTYzODcyMTAyMTk3NDQ3NDVlMmY1NGFmYWFlYmFkOGUzYWIxMzc1MTAzMGZjNGRhNDNmMGQyMjQwYTdkMjI5IiwidGFnIjoiIn0%3D; x-clockwork=%7B%22requestId%22%3A%221754640206-2859-*********%22%2C%22version%22%3A%225.3.4%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%2299dfe367%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "App\\Livewire\\Project\\EPerolehan", "getData": {"tab": "tracking-diary"}, "postData": [], "requestData": "", "sessionData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "_previous": {"__type__": "array", "url": "http://localhost:8000/ep?tab=tracking-diary"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 4}, "authenticatedUser": {"id": 4, "username": "<EMAIL>", "email": "<EMAIL>", "name": "IQBAL FIKRI MOHAMED MISMAN ."}, "cookies": {"_ga": "GA1.1.913056526.1747301190", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "1751960299", "sugar_user_theme": "SuiteP", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0=", "snipeit_session": "fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm", "optional_user_info_open": "true", "snipeit_passport_token": "*removed*", "optional_info_open": "false", "ep_kontrak_session": "eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6InFac05oYTBVVldnQ1VTeGF2UzJIbGc9PSIsInZhbHVlIjoiNWxBekF1ZHlLTjZRNHZuS1IyYys4bm1aR1QvQ1djdUQ0NnB3M1U3Sy82eWl2cWx5T0JFSWhha3YwZXRMUnhmYmlpdWQrMjVGNmdUckg2YjFUeGgxN1V0TnJ1eGhMcWpVZDd5QXFyUENMOGJCVDU5QW0yWnBRV0hRc3plSWltVlMiLCJtYWMiOiI3MjJiZWRhY2FjN2UyYzNmYTU3NjliYmYxZGFmNTY3MGU4YzY3NDNmM2YwMjFjNjllYmIzMmMzYjQ4ZjM5YjdjIiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6InlQdUdVbUROMWRrMjEzWUJLMUZKVFE9PSIsInZhbHVlIjoicFV6bFNrREt6K0RIeFg2c3FBNEd4dTF2bUlQOXNPSXE2aWlJMXQzMXUzUHhSNlNPUklWUTVMOXpScjZ3MTQ4QXFZUjhFaGF6QUcrbEZmVnNUNlh5TDdlMXJiUkNVVUFZV3dQZUxPMGVraVlUcnRFU3g1T3lBVXlsYkZKOG5YZjQiLCJtYWMiOiIwZTYzODcyMTAyMTk3NDQ3NDVlMmY1NGFmYWFlYmFkOGUzYWIxMzc1MTAzMGZjNGRhNDNmMGQyMjQwYTdkMjI5IiwidGFnIjoiIn0=", "x-clockwork": "{\"requestId\":\"1754640206-2859-*********\",\"version\":\"5.3.4\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"99dfe367\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": 1754640213.171277, "responseStatus": 200, "responseDuration": 1482.3310375213623, "memoryUsage": 31457280, "middleware": ["web", "auth:web", "log.activity", "route.permission"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB' LIMIT 1", "duration": 44.8, "connection": "epss2", "time": 1754640212.165256, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 4 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 10.86, "connection": "epss2", "time": 1754640212.236022, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 13.98, "connection": "epss2", "time": 1754640212.260854, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 8.06, "connection": "epss2", "time": 1754640212.275998, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 10.05, "connection": "epss2", "time": 1754640212.340369, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 8.16, "connection": "epss2", "time": 1754640212.3514829, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 19.1, "connection": "epss2", "time": 1754640212.3608592, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 9, "connection": "epss2", "time": 1754640212.381553, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 8.97, "connection": "epss2", "time": 1754640212.3926349, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 8.74, "connection": "epss2", "time": 1754640212.4035301, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 8.1, "connection": "epss2", "time": 1754640212.413998, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 9.06, "connection": "epss2", "time": 1754640212.423429, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 14.03, "connection": "epss2", "time": 1754640212.434019, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 8.66, "connection": "epss2", "time": 1754640212.4494748, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 11.98, "connection": "epss2", "time": 1754640212.4600089, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 8.35, "connection": "epss2", "time": 1754640212.4735892, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.45, "connection": "epss2", "time": 1754640212.483797, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.12, "connection": "epss2", "time": 1754640212.493635, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 17, "connection": "epss2", "time": 1754640212.50307, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.43, "connection": "epss2", "time": 1754640212.521593, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 8.44, "connection": "epss2", "time": 1754640212.531736, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 8.44, "connection": "epss2", "time": 1754640212.5419, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 9.17, "connection": "epss2", "time": 1754640212.552248, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 14.55, "connection": "epss2", "time": 1754640212.562777, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 13.3, "connection": "epss2", "time": 1754640212.5793052, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 8.82, "connection": "epss2", "time": 1754640212.594339, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.45, "connection": "epss2", "time": 1754640212.60519, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.53, "connection": "epss2", "time": 1754640212.6154292, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 8.77, "connection": "epss2", "time": 1754640212.625637, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 8.33, "connection": "epss2", "time": 1754640212.635973, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 15.45, "connection": "epss2", "time": 1754640212.646364, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 12.08, "connection": "epss2", "time": 1754640212.664443, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.54, "connection": "epss2", "time": 1754640212.679131, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.2, "connection": "epss2", "time": 1754640212.6893132, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8.61, "connection": "epss2", "time": 1754640212.699268, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8.2, "connection": "epss2", "time": 1754640212.7093542, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 13.69, "connection": "epss2", "time": 1754640212.719645, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 8.72, "connection": "epss2", "time": 1754640212.7349322, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 8.55, "connection": "epss2", "time": 1754640212.745109, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 8.6, "connection": "epss2", "time": 1754640212.754867, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 12.85, "connection": "epss2", "time": 1754640212.765585, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.64, "connection": "epss2", "time": 1754640212.780166, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 15.84, "connection": "epss2", "time": 1754640212.790747, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 8.64, "connection": "epss2", "time": 1754640212.807766, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.57, "connection": "epss2", "time": 1754640212.8178651, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.79, "connection": "epss2", "time": 1754640212.82794, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 9, "connection": "epss2", "time": 1754640212.838814, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 8.41, "connection": "epss2", "time": 1754640212.850195, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 21.11, "connection": "epss2", "time": 1754640212.860437, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 9.39, "connection": "epss2", "time": 1754640212.883077, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 10.44, "connection": "epss2", "time": 1754640212.8937929, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.06, "connection": "epss2", "time": 1754640212.905827, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 9.08, "connection": "epss2", "time": 1754640212.915131, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 8.34, "connection": "epss2", "time": 1754640212.925317, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 16.31, "connection": "epss2", "time": 1754640212.935798, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 9.15, "connection": "epss2", "time": 1754640212.954917, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 9.04, "connection": "epss2", "time": 1754640212.966898, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 14.11, "connection": "epss2", "time": 1754640212.97841, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 9.75, "connection": "epss2", "time": 1754640212.9946032, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 15.91, "connection": "epss2", "time": 1754640213.0057871, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.98, "connection": "epss2", "time": 1754640213.02357, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.73, "connection": "epss2", "time": 1754640213.034044, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 8.43, "connection": "epss2", "time": 1754640213.044663, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 8.4, "connection": "epss2", "time": 1754640213.0548542, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 8.01, "connection": "epss2", "time": 1754640213.064214, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 13.09, "connection": "epss2", "time": 1754640213.0735781, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 8.84, "connection": "epss2", "time": 1754640213.0878909, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 7.93, "connection": "epss2", "time": 1754640213.098355, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoid2dBM1dyMlFMeklWUWgzRzFpYXU1aFpZZmVmeENpRmVpbGtrTHA3NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDM6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9lcD90YWI9dHJhY2tpbmctZGlhcnkiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', `last_activity` = 1754640213, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB'", "duration": 15.64, "connection": "epss2", "time": 1754640213.155106, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 69, "databaseSlowQueries": 0, "databaseSelects": 68, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 750.8199999999998, "cacheQueries": [{"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640212.275333, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640212.284522, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640212.350818, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640212.360013, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640212.380573, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640212.391299, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640212.402375, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640212.413071, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640212.422534, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640212.433141, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640212.448472, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640212.458919, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640212.472621, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640212.482557, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640212.492991, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640212.502101, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640212.520613, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640212.530852, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640212.54064, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640212.551148, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640212.561993, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640212.577968, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640212.593438, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640212.603833, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640212.614522, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640212.624573, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640212.635, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640212.64476, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640212.662843, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640212.677707, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640212.68825, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640212.698163, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640212.708463, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640212.71817, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640212.734176, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640212.744122, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640212.75419, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640212.764103, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640212.779269, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640212.789516, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640212.807206, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640212.816926, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640212.827002, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640212.837483, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640212.848754, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640212.859541, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640212.881958, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640212.892931, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640212.904988, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640212.914422, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640212.924525, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640212.934372, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640212.953401, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640212.965175, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640212.97716, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640212.993375, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640213.004978, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640213.022289, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640213.032984, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640213.043554, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640213.053702, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640213.063637, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640213.072711, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640213.087221, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640213.097224, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640213.106814, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}], "cacheReads": 66, "cacheHits": 66, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754640212.131296, "end": 1754640213.171162, "duration": 1039.8659706115723, "color": null, "data": null}], "log": [{"message": "User Activity", "exception": null, "context": {"__type__": "array", "user_id": 4, "name": "IQBAL FIKRI MOHAMED MISMAN .", "email": "<EMAIL>", "url": "http://localhost:8000/ep?tab=tracking-diary", "method": "GET", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "level": "info", "time": 1754640212.255918, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 16, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "POST", "uri": "download", "name": "download", "action": "App\\Http\\Controllers\\ExportController@downloadUom", "middleware": ["web"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1754640212.296879, "end": 1754640212.296879, "duration": 0, "color": null, "data": {"name": "livewire.project.ep.index", "data": {"__type__": "array", "activeTab": "tracking-diary"}}}, {"description": "Rendering a view", "start": 1754640212.313085, "end": 1754640212.313085, "duration": 0, "color": null, "data": {"name": "livewire.fulfilment.tracking-diary", "data": {"__type__": "array", "listdata": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": false}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__type__": "recursion"}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__type__": "recursion"}, "listdata": {"__type__": "array"}, "allTrackingData": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "allTrackingData": {"__type__": "array"}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": 1754640212.315261, "end": 1754640212.315261, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.search-form", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": false}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "listdata": {"__type__": "array"}, "allTrackingData": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "allTrackingData": {"__type__": "array"}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": 1754640212.316833, "end": 1754640212.316833, "duration": 0, "color": null, "data": {"name": "partials.fulfilment.tracking-diary.document-details", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Module\\Fulfilment\\TrackingDiary", "*__id": "pirQegaUpEaH0FhPAHdV", "*__name": "module.fulfilment.tracking-diary", "*listeners": {"__type__": "array"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array", "0": {"__class__": "Livewire\\Attributes\\Url", "*component": {"__type__": "recursion"}, "*subTarget": null, "*subName": "<PERSON>ian", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "<PERSON>ian", "as": null, "history": false, "keep": false, "except": null, "nullable": false}, "1": {"__class__": "Livewire\\Attributes\\Validate", "*component": {"__type__": "recursion"}, "*subTarget": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "*subName": "documentNumber", "*level": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeLevel", "name": "PROPERTY"}, "*levelName": "trackingSearchForm.documentNumber", "rule": "required|min:2", "*attribute": null, "*as": null, "*message": "Please enter a valid document number", "*onUpdate": true, "*translate": true}}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "*fulfilmentService": {"__class__": "App\\Services\\FulfilmentService"}, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "listdata": {"__type__": "array"}, "allTrackingData": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}, "listdata": {"__type__": "array"}, "supplier": null, "smAppl": null, "preparedPtj": null, "issuedPtj": null, "createdPtj": null, "chargePtj": null, "poco_no": null, "sqInfo": null, "carian": null, "errorMessage": null, "isSearching": false, "trackingSearchForm": {"__class__": "App\\Livewire\\Forms\\Fulfilment\\TrackingDiarySearchForm", "*component": {"__type__": "recursion"}, "*propertyName": "trackingSearchForm", "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber": "required|min:2"}}, "*messagesFromOutside": {"__type__": "array", "0": {"__type__": "array", "documentNumber.required|min": "Please enter a valid document number"}}, "*validationAttributesFromOutside": {"__type__": "array"}, "documentNumber": ""}, "allTrackingData": {"__type__": "array"}, "isModalOpen": false, "modalTitle": "", "modalType": "", "modalDocNo": "", "listDataWorkflow": {"__type__": "array"}, "listDataDoFn": {"__type__": "array"}, "listDataYepMenu": {"__type__": "array"}, "modalData": {"__type__": "array"}, "isLoading": false}}}, {"description": "Rendering a view", "start": 1754640212.329815, "end": 1754640212.329815, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:&quot;tracking-diary&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;PZIsceRLwZt5t1Psvec4&quot;,&quot;name&quot;:&quot;project.e-perolehan&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-202309408-5&quot;:[&quot;div&quot;,&quot;pirQegaUpEaH0FhPAHdV&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c60d7c26314f9f7bcea970d25bf5d163f1b5219a643a19e96fc7cab85839e15b&quot;}\" wire:effects=\"[]\" wire:id=\"PZIsceRLwZt5t1Psvec4\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]-->                    <div wire:snapshot=\"{&quot;data&quot;:{&quot;trackingSearchForm&quot;:[{&quot;documentNumber&quot;:&quot;&quot;},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Forms\\\\Fulfilment\\\\TrackingDiarySearchForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;listdata&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;allTrackingData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;supplier&quot;:null,&quot;smAppl&quot;:null,&quot;preparedPtj&quot;:null,&quot;issuedPtj&quot;:null,&quot;createdPtj&quot;:null,&quot;chargePtj&quot;:null,&quot;poco_no&quot;:null,&quot;sqInfo&quot;:null,&quot;carian&quot;:null,&quot;errorMessage&quot;:null,&quot;isSearching&quot;:false,&quot;isModalOpen&quot;:false,&quot;modalTitle&quot;:&quot;&quot;,&quot;modalType&quot;:&quot;&quot;,&quot;modalDocNo&quot;:&quot;&quot;,&quot;listDataWorkflow&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataDoFn&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataYepMenu&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;modalData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isLoading&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;pirQegaUpEaH0FhPAHdV&quot;,&quot;name&quot;:&quot;module.fulfilment.tracking-diary&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;3791712019-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;4c694fd08f44764d28c9984a67eb955e2603955fdfb616e476d70e348be82a60&quot;}\" wire:effects=\"{&quot;scripts&quot;:{&quot;3791712019-0&quot;:&quot;&lt;script&gt;\\n    \\/\\/ Modal handling\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const element = document.querySelector(&#039;#documentDetailsModal&#039;);\\n            if (element) {\\n                const modal = KTModal.getInstance(element);\\n                if (!modal) {\\n                    \\/\\/ Initialize modal if not already initialized\\n                    new KTModal(element);\\n                    element.querySelector(&#039;[data-modal-toggle=\\&quot;#documentDetailsModal\\&quot;]&#039;).click();\\n                } else {\\n                    modal.show();\\n                }\\n            }\\n        }, 0);\\n    });\\n\\n    \\/\\/ Initialize datatable for all detail tables when modal opens\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const options = {\\n                pageSizes: [5, 10, 20, 50],\\n                stateSave: false\\n            };\\n            \\n            \\/\\/ Initialize workflow details table if it exists\\n            const workflowTable = document.querySelector(&#039;#workflow-details-table&#039;);\\n            if (workflowTable) {\\n                new KTDataTable(workflowTable, options);\\n            }\\n            \\n            \\/\\/ Initialize DO\\/FN details table if it exists\\n            const dofnTable = document.querySelector(&#039;#dofn-details-table&#039;);\\n            if (dofnTable) {\\n                new KTDataTable(dofnTable, options);\\n            }\\n            \\n            \\/\\/ Initialize YEP menu details table if it exists\\n            const yepmenuTable = document.querySelector(&#039;#yepmenu-details-table&#039;);\\n            if (yepmenuTable) {\\n                new KTDataTable(yepmenuTable, options);\\n            }\\n        }, 0);\\n    });\\n&lt;\\/script&gt;\\n    &quot;},&quot;url&quot;:{&quot;carian&quot;:{&quot;as&quot;:null,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null}},&quot;dispatches&quot;:[{&quot;name&quot;:&quot;refresh-tracking-diary-table&quot;,&quot;params&quot;:[]}]}\" wire:id=\"pirQegaUpEaH0FhPAHdV\" wire:replace class=\"\">\n    <div class=\"grid gap-5 lg:gap-7.5\">\n        <div class=\"flex flex-col gap-5\">\n            <div class=\"card\">\n    <div class=\"card-body\">\n        <form wire:submit=\"search\" class=\"flex flex-col gap-5\">\n            <div class=\"flex items-baseline flex-wrap lg:flex-nowrap gap-2.5\">\n                <label class=\"form-label max-w-56 font-normal text-gray-900\" for=\"search\">\n                    Document Number\n                </label>\n                <div class=\"flex flex-col gap-1 w-full lg:max-w-md\">\n                    <div class=\"input-group\">\n                        <input id=\"search\" \n                               type=\"text\" \n                               class=\"input\" \n                               placeholder=\"Enter document number\"\n                               wire:model=\"trackingSearchForm.documentNumber\">\n                        <button class=\"btn btn-primary\" type=\"submit\" wire:loading.attr=\"disabled\" wire:target=\"search\">\n                            <i class=\"ki-filled ki-magnifier\" wire:loading.remove wire:target=\"search\"></i>\n                            <i class=\"ki-filled ki-loading animate-spin\" wire:loading wire:target=\"search\"></i>\n                            <span wire:loading.remove wire:target=\"search\">Search</span>\n                            <span wire:loading wire:target=\"search\">Searching...</span>\n                        </button>\n                    </div>\n                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                </div>\n            </div>\n        </form>\n    </div>\n</div>\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n\n            <div wire:replace data-modal=\"true\" id=\"documentDetailsModal\" wire:ignore.self class=\"modal\">\n    <div class=\"modal-content max-w-[800px]\">\n        <div class=\"modal-header\">\n            <h3 class=\"modal-title\"></h3>\n            <button class=\"btn btn-xs btn-icon btn-light\" data-modal-dismiss=\"true\" wire:click=\"closeModal\">\n                <i class=\"ki-outline ki-cross\"></i>\n            </button>\n        </div>\n        <div class=\"modal-body\">\n            <!--[if BLOCK]><![endif]-->                <div>\n                    <div class=\"border-t pt-4 mt-4\">\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                    </div>\n                </div>\n            <!--[if ENDBLOCK]><![endif]-->\n\n            <div class=\"text-end mt-5\">\n                <button type=\"button\" class=\"btn btn-light\" data-modal-dismiss=\"true\"\n                    wire:click=\"closeModal\">Close</button>\n            </div>\n        </div>\n    </div>\n</div>\n\n            </div>\n    </div>\n</div>                <!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.app", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1754640212.333162, "end": 1754640212.333162, "duration": 0, "color": null, "data": {"name": "layouts.app", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:&quot;tracking-diary&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;PZIsceRLwZt5t1Psvec4&quot;,&quot;name&quot;:&quot;project.e-perolehan&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-202309408-5&quot;:[&quot;div&quot;,&quot;pirQegaUpEaH0FhPAHdV&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c60d7c26314f9f7bcea970d25bf5d163f1b5219a643a19e96fc7cab85839e15b&quot;}\" wire:effects=\"[]\" wire:id=\"PZIsceRLwZt5t1Psvec4\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]-->                    <div wire:snapshot=\"{&quot;data&quot;:{&quot;trackingSearchForm&quot;:[{&quot;documentNumber&quot;:&quot;&quot;},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Forms\\\\Fulfilment\\\\TrackingDiarySearchForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;listdata&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;allTrackingData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;supplier&quot;:null,&quot;smAppl&quot;:null,&quot;preparedPtj&quot;:null,&quot;issuedPtj&quot;:null,&quot;createdPtj&quot;:null,&quot;chargePtj&quot;:null,&quot;poco_no&quot;:null,&quot;sqInfo&quot;:null,&quot;carian&quot;:null,&quot;errorMessage&quot;:null,&quot;isSearching&quot;:false,&quot;isModalOpen&quot;:false,&quot;modalTitle&quot;:&quot;&quot;,&quot;modalType&quot;:&quot;&quot;,&quot;modalDocNo&quot;:&quot;&quot;,&quot;listDataWorkflow&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataDoFn&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataYepMenu&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;modalData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isLoading&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;pirQegaUpEaH0FhPAHdV&quot;,&quot;name&quot;:&quot;module.fulfilment.tracking-diary&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;3791712019-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;4c694fd08f44764d28c9984a67eb955e2603955fdfb616e476d70e348be82a60&quot;}\" wire:effects=\"{&quot;scripts&quot;:{&quot;3791712019-0&quot;:&quot;&lt;script&gt;\\n    \\/\\/ Modal handling\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const element = document.querySelector(&#039;#documentDetailsModal&#039;);\\n            if (element) {\\n                const modal = KTModal.getInstance(element);\\n                if (!modal) {\\n                    \\/\\/ Initialize modal if not already initialized\\n                    new KTModal(element);\\n                    element.querySelector(&#039;[data-modal-toggle=\\&quot;#documentDetailsModal\\&quot;]&#039;).click();\\n                } else {\\n                    modal.show();\\n                }\\n            }\\n        }, 0);\\n    });\\n\\n    \\/\\/ Initialize datatable for all detail tables when modal opens\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const options = {\\n                pageSizes: [5, 10, 20, 50],\\n                stateSave: false\\n            };\\n            \\n            \\/\\/ Initialize workflow details table if it exists\\n            const workflowTable = document.querySelector(&#039;#workflow-details-table&#039;);\\n            if (workflowTable) {\\n                new KTDataTable(workflowTable, options);\\n            }\\n            \\n            \\/\\/ Initialize DO\\/FN details table if it exists\\n            const dofnTable = document.querySelector(&#039;#dofn-details-table&#039;);\\n            if (dofnTable) {\\n                new KTDataTable(dofnTable, options);\\n            }\\n            \\n            \\/\\/ Initialize YEP menu details table if it exists\\n            const yepmenuTable = document.querySelector(&#039;#yepmenu-details-table&#039;);\\n            if (yepmenuTable) {\\n                new KTDataTable(yepmenuTable, options);\\n            }\\n        }, 0);\\n    });\\n&lt;\\/script&gt;\\n    &quot;},&quot;url&quot;:{&quot;carian&quot;:{&quot;as&quot;:null,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null}},&quot;dispatches&quot;:[{&quot;name&quot;:&quot;refresh-tracking-diary-table&quot;,&quot;params&quot;:[]}]}\" wire:id=\"pirQegaUpEaH0FhPAHdV\" wire:replace class=\"\">\n    <div class=\"grid gap-5 lg:gap-7.5\">\n        <div class=\"flex flex-col gap-5\">\n            <div class=\"card\">\n    <div class=\"card-body\">\n        <form wire:submit=\"search\" class=\"flex flex-col gap-5\">\n            <div class=\"flex items-baseline flex-wrap lg:flex-nowrap gap-2.5\">\n                <label class=\"form-label max-w-56 font-normal text-gray-900\" for=\"search\">\n                    Document Number\n                </label>\n                <div class=\"flex flex-col gap-1 w-full lg:max-w-md\">\n                    <div class=\"input-group\">\n                        <input id=\"search\" \n                               type=\"text\" \n                               class=\"input\" \n                               placeholder=\"Enter document number\"\n                               wire:model=\"trackingSearchForm.documentNumber\">\n                        <button class=\"btn btn-primary\" type=\"submit\" wire:loading.attr=\"disabled\" wire:target=\"search\">\n                            <i class=\"ki-filled ki-magnifier\" wire:loading.remove wire:target=\"search\"></i>\n                            <i class=\"ki-filled ki-loading animate-spin\" wire:loading wire:target=\"search\"></i>\n                            <span wire:loading.remove wire:target=\"search\">Search</span>\n                            <span wire:loading wire:target=\"search\">Searching...</span>\n                        </button>\n                    </div>\n                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                </div>\n            </div>\n        </form>\n    </div>\n</div>\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n\n            <div wire:replace data-modal=\"true\" id=\"documentDetailsModal\" wire:ignore.self class=\"modal\">\n    <div class=\"modal-content max-w-[800px]\">\n        <div class=\"modal-header\">\n            <h3 class=\"modal-title\"></h3>\n            <button class=\"btn btn-xs btn-icon btn-light\" data-modal-dismiss=\"true\" wire:click=\"closeModal\">\n                <i class=\"ki-outline ki-cross\"></i>\n            </button>\n        </div>\n        <div class=\"modal-body\">\n            <!--[if BLOCK]><![endif]-->                <div>\n                    <div class=\"border-t pt-4 mt-4\">\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                    </div>\n                </div>\n            <!--[if ENDBLOCK]><![endif]-->\n\n            <div class=\"text-end mt-5\">\n                <button type=\"button\" class=\"btn btn-light\" data-modal-dismiss=\"true\"\n                    wire:click=\"closeModal\">Close</button>\n            </div>\n        </div>\n    </div>\n</div>\n\n            </div>\n    </div>\n</div>                <!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}, {"description": "Rendering a view", "start": 1754640213.108069, "end": 1754640213.108069, "duration": 0, "color": null, "data": {"name": "livewire.component.sidebar", "data": {"__type__": "array", "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}}}, {"description": "Rendering a view", "start": 1754640213.109272, "end": 1754640213.109272, "duration": 0, "color": null, "data": {"name": "partials.sidebar.header", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "uqtN9YFqcZs0aDxTydAx", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}}}, {"description": "Rendering a view", "start": 1754640213.110601, "end": 1754640213.110601, "duration": 0, "color": null, "data": {"name": "partials.sidebar.user-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "uqtN9YFqcZs0aDxTydAx", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han", "menuItem": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}, "loop": null}}}, {"description": "Rendering a view", "start": 1754640213.112314, "end": 1754640213.112314, "duration": 0, "color": null, "data": {"name": "partials.sidebar.item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "uqtN9YFqcZs0aDxTydAx", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han", "menuItem": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}, "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}}, {"description": "Rendering a view", "start": 1754640213.115677, "end": 1754640213.115677, "duration": 0, "color": null, "data": {"name": "partials.sidebar.simple-item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "uqtN9YFqcZs0aDxTydAx", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/ep", "activeTopLevelItem": "eper<PERSON>han", "menuItem": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": true}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 1, "count": 2, "first": true, "last": false, "odd": true, "even": false, "depth": 3, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 4, "count": 5, "first": true, "last": false, "odd": true, "even": false, "depth": 2, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}}}, "type": "section", "title": "Dashboard", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}, "level": 3, "linkPadding": "ps-6.5", "levelClasses": "ms-2 border-s border-s-gray-200 ps-3", "item": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "disabled": false, "badge": null}}}, {"description": "Rendering a view", "start": 1754640213.141616, "end": 1754640213.141616, "duration": 0, "color": null, "data": {"name": "livewire.component.header", "data": {"__type__": "array", "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "ePerolehan", "is_last": false, "route": "ep", "query": null}, "1": {"__type__": "array", "title": "Tracking Diary", "is_last": true, "route": "ep", "query": {"__type__": "array", "tab": "tracking-diary"}}}}}}, {"description": "Rendering a view", "start": 1754640213.14352, "end": 1754640213.14352, "duration": 0, "color": null, "data": {"name": "components.footer", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array"}}, "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:&quot;tracking-diary&quot;},&quot;memo&quot;:{&quot;id&quot;:&quot;PZIsceRLwZt5t1Psvec4&quot;,&quot;name&quot;:&quot;project.e-perolehan&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-202309408-5&quot;:[&quot;div&quot;,&quot;pirQegaUpEaH0FhPAHdV&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c60d7c26314f9f7bcea970d25bf5d163f1b5219a643a19e96fc7cab85839e15b&quot;}\" wire:effects=\"[]\" wire:id=\"PZIsceRLwZt5t1Psvec4\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]-->                    <div wire:snapshot=\"{&quot;data&quot;:{&quot;trackingSearchForm&quot;:[{&quot;documentNumber&quot;:&quot;&quot;},{&quot;class&quot;:&quot;App\\\\Livewire\\\\Forms\\\\Fulfilment\\\\TrackingDiarySearchForm&quot;,&quot;s&quot;:&quot;form&quot;}],&quot;listdata&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;allTrackingData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;supplier&quot;:null,&quot;smAppl&quot;:null,&quot;preparedPtj&quot;:null,&quot;issuedPtj&quot;:null,&quot;createdPtj&quot;:null,&quot;chargePtj&quot;:null,&quot;poco_no&quot;:null,&quot;sqInfo&quot;:null,&quot;carian&quot;:null,&quot;errorMessage&quot;:null,&quot;isSearching&quot;:false,&quot;isModalOpen&quot;:false,&quot;modalTitle&quot;:&quot;&quot;,&quot;modalType&quot;:&quot;&quot;,&quot;modalDocNo&quot;:&quot;&quot;,&quot;listDataWorkflow&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataDoFn&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;listDataYepMenu&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;modalData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isLoading&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;pirQegaUpEaH0FhPAHdV&quot;,&quot;name&quot;:&quot;module.fulfilment.tracking-diary&quot;,&quot;path&quot;:&quot;ep&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;3791712019-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;4c694fd08f44764d28c9984a67eb955e2603955fdfb616e476d70e348be82a60&quot;}\" wire:effects=\"{&quot;scripts&quot;:{&quot;3791712019-0&quot;:&quot;&lt;script&gt;\\n    \\/\\/ Modal handling\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const element = document.querySelector(&#039;#documentDetailsModal&#039;);\\n            if (element) {\\n                const modal = KTModal.getInstance(element);\\n                if (!modal) {\\n                    \\/\\/ Initialize modal if not already initialized\\n                    new KTModal(element);\\n                    element.querySelector(&#039;[data-modal-toggle=\\&quot;#documentDetailsModal\\&quot;]&#039;).click();\\n                } else {\\n                    modal.show();\\n                }\\n            }\\n        }, 0);\\n    });\\n\\n    \\/\\/ Initialize datatable for all detail tables when modal opens\\n    Livewire.on(&#039;open-document-modal&#039;, () =&gt; {\\n        setTimeout(() =&gt; {\\n            const options = {\\n                pageSizes: [5, 10, 20, 50],\\n                stateSave: false\\n            };\\n            \\n            \\/\\/ Initialize workflow details table if it exists\\n            const workflowTable = document.querySelector(&#039;#workflow-details-table&#039;);\\n            if (workflowTable) {\\n                new KTDataTable(workflowTable, options);\\n            }\\n            \\n            \\/\\/ Initialize DO\\/FN details table if it exists\\n            const dofnTable = document.querySelector(&#039;#dofn-details-table&#039;);\\n            if (dofnTable) {\\n                new KTDataTable(dofnTable, options);\\n            }\\n            \\n            \\/\\/ Initialize YEP menu details table if it exists\\n            const yepmenuTable = document.querySelector(&#039;#yepmenu-details-table&#039;);\\n            if (yepmenuTable) {\\n                new KTDataTable(yepmenuTable, options);\\n            }\\n        }, 0);\\n    });\\n&lt;\\/script&gt;\\n    &quot;},&quot;url&quot;:{&quot;carian&quot;:{&quot;as&quot;:null,&quot;use&quot;:&quot;replace&quot;,&quot;alwaysShow&quot;:false,&quot;except&quot;:null}},&quot;dispatches&quot;:[{&quot;name&quot;:&quot;refresh-tracking-diary-table&quot;,&quot;params&quot;:[]}]}\" wire:id=\"pirQegaUpEaH0FhPAHdV\" wire:replace class=\"\">\n    <div class=\"grid gap-5 lg:gap-7.5\">\n        <div class=\"flex flex-col gap-5\">\n            <div class=\"card\">\n    <div class=\"card-body\">\n        <form wire:submit=\"search\" class=\"flex flex-col gap-5\">\n            <div class=\"flex items-baseline flex-wrap lg:flex-nowrap gap-2.5\">\n                <label class=\"form-label max-w-56 font-normal text-gray-900\" for=\"search\">\n                    Document Number\n                </label>\n                <div class=\"flex flex-col gap-1 w-full lg:max-w-md\">\n                    <div class=\"input-group\">\n                        <input id=\"search\" \n                               type=\"text\" \n                               class=\"input\" \n                               placeholder=\"Enter document number\"\n                               wire:model=\"trackingSearchForm.documentNumber\">\n                        <button class=\"btn btn-primary\" type=\"submit\" wire:loading.attr=\"disabled\" wire:target=\"search\">\n                            <i class=\"ki-filled ki-magnifier\" wire:loading.remove wire:target=\"search\"></i>\n                            <i class=\"ki-filled ki-loading animate-spin\" wire:loading wire:target=\"search\"></i>\n                            <span wire:loading.remove wire:target=\"search\">Search</span>\n                            <span wire:loading wire:target=\"search\">Searching...</span>\n                        </button>\n                    </div>\n                    <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                </div>\n            </div>\n        </form>\n    </div>\n</div>\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if ENDBLOCK]><![endif]-->\n\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n\n            <div wire:replace data-modal=\"true\" id=\"documentDetailsModal\" wire:ignore.self class=\"modal\">\n    <div class=\"modal-content max-w-[800px]\">\n        <div class=\"modal-header\">\n            <h3 class=\"modal-title\"></h3>\n            <button class=\"btn btn-xs btn-icon btn-light\" data-modal-dismiss=\"true\" wire:click=\"closeModal\">\n                <i class=\"ki-outline ki-cross\"></i>\n            </button>\n        </div>\n        <div class=\"modal-body\">\n            <!--[if BLOCK]><![endif]-->                <div>\n                    <div class=\"border-t pt-4 mt-4\">\n                        <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n                    </div>\n                </div>\n            <!--[if ENDBLOCK]><![endif]-->\n\n            <div class=\"text-end mt-5\">\n                <button type=\"button\" class=\"btn btn-light\" data-modal-dismiss=\"true\"\n                    wire:click=\"closeModal\">Close</button>\n            </div>\n        </div>\n    </div>\n</div>\n\n            </div>\n    </div>\n</div>                <!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "711f8eab"}