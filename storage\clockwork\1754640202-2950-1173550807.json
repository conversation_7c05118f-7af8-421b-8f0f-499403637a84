{"id": "1754640202-2950-1173550807", "version": 1, "type": "request", "time": 1754640201.974909, "method": "GET", "url": "http://localhost:8000/report", "uri": "/report", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "referer": ["http://localhost:8000/home"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.1747301190; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=1751960299; sugar_user_theme=SuiteP; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0%3D; snipeit_session=fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm; optional_user_info_open=true; snipeit_passport_token=eyJpdiI6IlQrMExxNDVtYWlGQlJ4YTRzcEQwZnc9PSIsInZhbHVlIjoiclo3MFpxRGlCbWhxRmJPOGhWQzNRYTN5Rlk5VmNlZDY2Sm1wczgxRFRzajI0SVBIaGhzQWs0aDBjYWFubndTMXJDUkd4YUZLbFFFMUo0NmZSa3pWRkl6SnJDOW1Hc2w1dXY2dzE3QTUrV1owVVlnU01IS0pIeDdyOGhtTjVtTjZCLzlNSmVYZE1LRG13amdJOC9MRU9EdlRIdit5ZmlZVm8wckJLZE5mVTJhWGYzMGFKVlZvemZodHhaS2lvb3BKZXQ3SzNEcnVBazFBNjZLMGhjWDlubWZacng1NFk0TjBGc0xBc3VhUno5N0hrRzAzbi9jNnE1SXY3K1Z1UlAwdk1RMXVjOThKWWZ4UDEwMzI3YVhRbG1pSUVNZUZ3R2wxdUg0amdMYzcvRFNMaGVWNG5ub3pyeWZnNy9mczI3cXEiLCJtYWMiOiI1NDg4NTEyMTU4NDc4NGNhNDU4Mjg4MGYxOWEwNzUxMDJiMjQxNjJhNzY4OTI0NzVkNzc0NmEyZWQ2NTgyMmEyIiwidGFnIjoiIn0%3D; optional_info_open=false; ep_kontrak_session=eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InZ3anpUZ1YwSkt3MlA3dEc1VEdtY3c9PSIsInZhbHVlIjoiZy9vR3FOYUh3ejlZWHhEVW5MNWJiMHVBSXBKT1FCK3JZd3Y2SEx4K0tzdXYwdndnelZFQWgrbzJhT002MlBrVWExTUlpMjc3QWFpNFNWa3RPdmJXQkhkbGtVdS9COUZlM0wxTDcxTGFka2NtcS9rTTRJR3VmbGVkRmQrbkpKdk0iLCJtYWMiOiJmODNhNGYwNmY1NGNkOTQ5ZTM0ODZlYzAzMzIyZTA4NmIzZTAyNTg1NDI3MGRlODMxNjNlOWRiYWRjZGU3MjJmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImFLNjlKYlhtYmJZbmxnZTdGeDMxVmc9PSIsInZhbHVlIjoiRGJPWjQ4VDFwbGFCR1pneTNTcG5IQ0t4cU51ZGhmMUJQR2VTMTFvb0lTeGJpU3hMd3dtRlZKemJsZGVyZXlSZC9NcU9peVJZMU01TmtldUh5RjNzMEtIUm11Z1BtMVdPb2JvVERZZWh2ZVFHK3JLdjJ4aXFaL0o0YXFpWThvRnAiLCJtYWMiOiJmOTE1YWFlNGYzNzdkYTI5YjM4NTNkYmY4MDA0ZDBmZjZkZWQyN2FiNGFiY2Q1OTg4OGZkMDVmN2FiODUxOTgxIiwidGFnIjoiIn0%3D; x-clockwork=%7B%22requestId%22%3A%221754640184-0895-1870832142%22%2C%22version%22%3A%225.3.4%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%228a18ba3e%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "App\\Livewire\\Project\\Report", "getData": [], "postData": [], "requestData": "", "sessionData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "_previous": {"__type__": "array", "url": "http://localhost:8000/report"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 4}, "authenticatedUser": {"id": 4, "username": "<EMAIL>", "email": "<EMAIL>", "name": "IQBAL FIKRI MOHAMED MISMAN ."}, "cookies": {"_ga": "GA1.1.913056526.1747301190", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "1751960299", "sugar_user_theme": "SuiteP", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0=", "snipeit_session": "fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm", "optional_user_info_open": "true", "snipeit_passport_token": "*removed*", "optional_info_open": "false", "ep_kontrak_session": "eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6InZ3anpUZ1YwSkt3MlA3dEc1VEdtY3c9PSIsInZhbHVlIjoiZy9vR3FOYUh3ejlZWHhEVW5MNWJiMHVBSXBKT1FCK3JZd3Y2SEx4K0tzdXYwdndnelZFQWgrbzJhT002MlBrVWExTUlpMjc3QWFpNFNWa3RPdmJXQkhkbGtVdS9COUZlM0wxTDcxTGFka2NtcS9rTTRJR3VmbGVkRmQrbkpKdk0iLCJtYWMiOiJmODNhNGYwNmY1NGNkOTQ5ZTM0ODZlYzAzMzIyZTA4NmIzZTAyNTg1NDI3MGRlODMxNjNlOWRiYWRjZGU3MjJmIiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6ImFLNjlKYlhtYmJZbmxnZTdGeDMxVmc9PSIsInZhbHVlIjoiRGJPWjQ4VDFwbGFCR1pneTNTcG5IQ0t4cU51ZGhmMUJQR2VTMTFvb0lTeGJpU3hMd3dtRlZKemJsZGVyZXlSZC9NcU9peVJZMU01TmtldUh5RjNzMEtIUm11Z1BtMVdPb2JvVERZZWh2ZVFHK3JLdjJ4aXFaL0o0YXFpWThvRnAiLCJtYWMiOiJmOTE1YWFlNGYzNzdkYTI5YjM4NTNkYmY4MDA0ZDBmZjZkZWQyN2FiNGFiY2Q1OTg4OGZkMDVmN2FiODUxOTgxIiwidGFnIjoiIn0=", "x-clockwork": "{\"requestId\":\"1754640184-0895-1870832142\",\"version\":\"5.3.4\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"8a18ba3e\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": 1754640203.484375, "responseStatus": 200, "responseDuration": 1509.4659328460693, "memoryUsage": 29360128, "middleware": ["web", "auth:web", "log.activity", "route.permission"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB' LIMIT 1", "duration": 39.14, "connection": "epss2", "time": 1754640202.559759, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 4 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 12.95, "connection": "epss2", "time": 1754640202.637113, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 9.25, "connection": "epss2", "time": 1754640202.6708891, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.6, "connection": "epss2", "time": 1754640202.681016, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 8.88, "connection": "epss2", "time": 1754640202.718615, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 14.32, "connection": "epss2", "time": 1754640202.728955, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 8.3, "connection": "epss2", "time": 1754640202.744288, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 7.89, "connection": "epss2", "time": 1754640202.753698, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 8.25, "connection": "epss2", "time": 1754640202.762705, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 7.82, "connection": "epss2", "time": 1754640202.7718642, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 10.74, "connection": "epss2", "time": 1754640202.780681, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 8.23, "connection": "epss2", "time": 1754640202.79237, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 9.31, "connection": "epss2", "time": 1754640202.801984, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 8.36, "connection": "epss2", "time": 1754640202.8126302, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 8.4, "connection": "epss2", "time": 1754640202.8227491, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 12.95, "connection": "epss2", "time": 1754640202.8328571, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.22, "connection": "epss2", "time": 1754640202.8474371, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.26, "connection": "epss2", "time": 1754640202.856881, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.26, "connection": "epss2", "time": 1754640202.866705, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.21, "connection": "epss2", "time": 1754640202.875849, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 8.43, "connection": "epss2", "time": 1754640202.885191, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 8.16, "connection": "epss2", "time": 1754640202.894939, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 9.94, "connection": "epss2", "time": 1754640202.904309, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 7.91, "connection": "epss2", "time": 1754640202.915109, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 7.75, "connection": "epss2", "time": 1754640202.924075, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 8.18, "connection": "epss2", "time": 1754640202.933069, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 13.18, "connection": "epss2", "time": 1754640202.942303, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.88, "connection": "epss2", "time": 1754640202.9564772, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 8.16, "connection": "epss2", "time": 1754640202.9669888, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 8.69, "connection": "epss2", "time": 1754640202.977336, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 8.9, "connection": "epss2", "time": 1754640202.988729, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 7.97, "connection": "epss2", "time": 1754640202.99946, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.4, "connection": "epss2", "time": 1754640203.008795, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.49, "connection": "epss2", "time": 1754640203.0198739, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8.46, "connection": "epss2", "time": 1754640203.029897, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 17.63, "connection": "epss2", "time": 1754640203.039589, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 7.91, "connection": "epss2", "time": 1754640203.058646, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 8.28, "connection": "epss2", "time": 1754640203.06751, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 8.69, "connection": "epss2", "time": 1754640203.077137, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 8.67, "connection": "epss2", "time": 1754640203.086943, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.83, "connection": "epss2", "time": 1754640203.0970628, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.48, "connection": "epss2", "time": 1754640203.107347, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 8.32, "connection": "epss2", "time": 1754640203.117071, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 9.43, "connection": "epss2", "time": 1754640203.126617, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.67, "connection": "epss2", "time": 1754640203.1373818, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 12.4, "connection": "epss2", "time": 1754640203.1476672, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 8.84, "connection": "epss2", "time": 1754640203.1611838, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 8.03, "connection": "epss2", "time": 1754640203.171328, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 8.29, "connection": "epss2", "time": 1754640203.180884, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 10.36, "connection": "epss2", "time": 1754640203.19084, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.77, "connection": "epss2", "time": 1754640203.202661, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.69, "connection": "epss2", "time": 1754640203.2132378, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 10, "connection": "epss2", "time": 1754640203.224007, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 8.34, "connection": "epss2", "time": 1754640203.2359622, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 14.28, "connection": "epss2", "time": 1754640203.246549, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 10.2, "connection": "epss2", "time": 1754640203.262273, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 8.24, "connection": "epss2", "time": 1754640203.274293, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 11.29, "connection": "epss2", "time": 1754640203.283999, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 8.51, "connection": "epss2", "time": 1754640203.2971778, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 15.93, "connection": "epss2", "time": 1754640203.3072991, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 9.82, "connection": "epss2", "time": 1754640203.325312, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.79, "connection": "epss2", "time": 1754640203.336655, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 13.83, "connection": "epss2", "time": 1754640203.347237, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 14.06, "connection": "epss2", "time": 1754640203.363149, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 9.48, "connection": "epss2", "time": 1754640203.379611, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 10.24, "connection": "epss2", "time": 1754640203.3910809, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 9.09, "connection": "epss2", "time": 1754640203.403587, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 16.98, "connection": "epss2", "time": 1754640203.414341, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoid2dBM1dyMlFMeklWUWgzRzFpYXU1aFpZZmVmeENpRmVpbGtrTHA3NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjg6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9yZXBvcnQiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', `last_activity` = 1754640203, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB'", "duration": 10.96, "connection": "epss2", "time": 1754640203.4728599, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 69, "databaseSlowQueries": 0, "databaseSelects": 68, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 698.17, "cacheQueries": [{"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640202.680514, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640202.689993, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640202.728261, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640202.743634, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640202.752925, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640202.762091, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640202.771261, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640202.780059, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640202.791735, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640202.801121, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640202.811954, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640202.821541, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640202.831784, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640202.846396, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640202.856165, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640202.865732, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640202.875315, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640202.884398, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640202.894111, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640202.903575, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640202.914599, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640202.923342, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640202.932485, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640202.941609, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640202.955858, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640202.96587, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640202.975939, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640202.986898, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640202.998772, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640203.007737, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640203.01813, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640203.029064, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640203.038818, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640203.057589, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640203.066945, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640203.076302, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640203.086297, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640203.096114, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640203.106495, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640203.116318, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640203.125989, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640203.136373, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640203.147046, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640203.16042, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640203.170466, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640203.18, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640203.189832, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640203.20179, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640203.212059, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640203.222862, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640203.234712, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640203.245192, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640203.261411, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640203.273198, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640203.282975, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640203.295906, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640203.306173, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640203.324085, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640203.335738, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640203.346142, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640203.361729, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640203.378374, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640203.389882, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640203.40213, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640203.413425, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640203.432157, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}], "cacheReads": 66, "cacheHits": 66, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754640202.51966, "end": 1754640203.484286, "duration": 964.6260738372803, "color": null, "data": null}], "log": [{"message": "User Activity", "exception": null, "context": {"__type__": "array", "user_id": 4, "name": "IQBAL FIKRI MOHAMED MISMAN .", "email": "<EMAIL>", "url": "http://localhost:8000/report", "method": "GET", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "level": "info", "time": 1754640202.664157, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 16, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "POST", "uri": "download", "name": "download", "action": "App\\Http\\Controllers\\ExportController@downloadUom", "middleware": ["web"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1754640202.702495, "end": 1754640202.702495, "duration": 0, "color": null, "data": {"name": "livewire.project.report.index", "data": {"__type__": "array", "activeTab": null}}}, {"description": "Rendering a view", "start": 1754640202.708863, "end": 1754640202.708863, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;GvaTfE5SWcBHhLVxq7Ud&quot;,&quot;name&quot;:&quot;project.report&quot;,&quot;path&quot;:&quot;report&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c16205521b7a038281eeccd8142d2f29ce611a1685ae29b0ce7fd9272fb3d53b&quot;}\" wire:effects=\"[]\" wire:id=\"GvaTfE5SWcBHhLVxq7Ud\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.app", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1754640202.71224, "end": 1754640202.71224, "duration": 0, "color": null, "data": {"name": "layouts.app", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;GvaTfE5SWcBHhLVxq7Ud&quot;,&quot;name&quot;:&quot;project.report&quot;,&quot;path&quot;:&quot;report&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c16205521b7a038281eeccd8142d2f29ce611a1685ae29b0ce7fd9272fb3d53b&quot;}\" wire:effects=\"[]\" wire:id=\"GvaTfE5SWcBHhLVxq7Ud\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}, {"description": "Rendering a view", "start": 1754640203.434068, "end": 1754640203.434068, "duration": 0, "color": null, "data": {"name": "livewire.component.sidebar", "data": {"__type__": "array", "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}}}, {"description": "Rendering a view", "start": 1754640203.436184, "end": 1754640203.436184, "duration": 0, "color": null, "data": {"name": "partials.sidebar.header", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "bw5WloC8xSyn0JpO99Ok", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}}}, {"description": "Rendering a view", "start": 1754640203.43839, "end": 1754640203.43839, "duration": 0, "color": null, "data": {"name": "partials.sidebar.user-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "bw5WloC8xSyn0JpO99Ok", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report", "menuItem": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}, "loop": null}}}, {"description": "Rendering a view", "start": 1754640203.440198, "end": 1754640203.440198, "duration": 0, "color": null, "data": {"name": "partials.sidebar.item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "bw5WloC8xSyn0JpO99Ok", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report", "menuItem": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}, "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}}, {"description": "Rendering a view", "start": 1754640203.443272, "end": 1754640203.443272, "duration": 0, "color": null, "data": {"name": "partials.sidebar.simple-item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "bw5WloC8xSyn0JpO99Ok", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/report", "activeTopLevelItem": "report", "menuItem": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 1, "count": 2, "first": true, "last": false, "odd": true, "even": false, "depth": 2, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}}, "type": "item", "title": "Today Transaction", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report?tab=today-trans", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}, "level": 2, "linkPadding": "ps-2.5", "levelClasses": "", "item": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "disabled": false, "badge": null}}}, {"description": "Rendering a view", "start": 1754640203.45789, "end": 1754640203.45789, "duration": 0, "color": null, "data": {"name": "livewire.component.header", "data": {"__type__": "array", "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "Report - Revenue", "is_last": true, "route": "report", "query": null}}}}}, {"description": "Rendering a view", "start": 1754640203.460755, "end": 1754640203.460755, "duration": 0, "color": null, "data": {"name": "components.footer", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array"}}, "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;GvaTfE5SWcBHhLVxq7Ud&quot;,&quot;name&quot;:&quot;project.report&quot;,&quot;path&quot;:&quot;report&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c16205521b7a038281eeccd8142d2f29ce611a1685ae29b0ce7fd9272fb3d53b&quot;}\" wire:effects=\"[]\" wire:id=\"GvaTfE5SWcBHhLVxq7Ud\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "ed07f4a2"}