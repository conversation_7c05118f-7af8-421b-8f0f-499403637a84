<div wire:loading.remove class="card card-grid min-w-full mt-4">
    <div class="card-header py-5 bg-dark">
        <h3 class="card-title text-white">
            <strong>Talkdesk POMS</strong> - Uploaded Data (<?php echo e(ucfirst($byType)); ?>)
        </h3>
    </div>
    <div class="card-body">
        <div data-datatable="false" data-datatable-page-size="10" id="talkdesk-data-table">
            <div class="scrollable-x-auto">
                <table class="table table-border" data-datatable-table="true">
                    <thead>
                        <tr>
                            <th class="text-center">Date Call</th>
                            <th class="text-center">ACD Call Offer</th>
                            <th class="text-center">ACD Call Handle</th>
                            <th class="text-center">Call WI Lvl</th>
                            <th class="text-center">Call Abandon Short</th>
                            <th class="text-center">Call Abandon Long</th>
                            <th class="text-center">Abandon Percentage</th>
                            <th class="text-center">Answer Percentage</th>
                            <th class="text-center">Service Level</th>
                            <th class="text-center">Insert Datetime</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td class="text-center"><?php echo e(date('d/m/Y', strtotime($row->date_call))); ?>

                                </td>
                                <td class="text-center"><?php echo e($row->acd_call_offer); ?></td>
                                <td class="text-center"><?php echo e($row->acd_call_handle); ?></td>
                                <td class="text-center"><?php echo e($row->call_WI_lvl); ?></td>
                                <td class="text-center"><?php echo e($row->call_abandon_short); ?></td>
                                <td class="text-center"><?php echo e($row->call_abandon_long); ?></td>
                                <td class="text-center"><?php echo e($row->abandon_percentage); ?></td>
                                <td class="text-center"><?php echo e($row->answer_percentage); ?></td>
                                <td class="text-center"><?php echo e($row->service_level); ?></td>
                                <td class="text-center">
                                    <?php echo e(date('d/m/Y h:i A', strtotime($row->mitel_insert_data_datetime))); ?>

                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="10" class="text-center text-muted">
                                    No data available for <?php echo e($byType); ?> view
                                </td>
                            </tr>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </tbody>
                </table>
            </div>
            <div
                class="card-footer justify-center md:justify-between flex-col md:flex-row gap-5 text-gray-600 text-2sm font-medium">
                <div class="flex items-center gap-2 order-2 md:order-1">
                    Show
                    <select class="select select-sm w-16" data-datatable-size="true" name="perpage">
                    </select>
                    per page
                </div>
                <div class="flex items-center gap-4 order-1 md:order-2">
                    <span data-datatable-info="true"></span>
                    <div class="pagination" data-datatable-pagination="true"></div>
                </div>
            </div>
        </div>
    </div>

</div><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/crm/poms-talkdesk/poms-talkdesk-datatable.blade.php ENDPATH**/ ?>