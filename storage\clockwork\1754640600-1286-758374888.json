{"id": "1754640600-1286-758374888", "version": 1, "type": "request", "time": 1754640599.721228, "method": "GET", "url": "http://localhost:8000/crm", "uri": "/crm", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-dest": ["document"], "referer": ["http://localhost:8000/home"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.1747301190; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=1751960299; sugar_user_theme=SuiteP; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0%3D; snipeit_session=fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm; optional_user_info_open=true; snipeit_passport_token=eyJpdiI6IlQrMExxNDVtYWlGQlJ4YTRzcEQwZnc9PSIsInZhbHVlIjoiclo3MFpxRGlCbWhxRmJPOGhWQzNRYTN5Rlk5VmNlZDY2Sm1wczgxRFRzajI0SVBIaGhzQWs0aDBjYWFubndTMXJDUkd4YUZLbFFFMUo0NmZSa3pWRkl6SnJDOW1Hc2w1dXY2dzE3QTUrV1owVVlnU01IS0pIeDdyOGhtTjVtTjZCLzlNSmVYZE1LRG13amdJOC9MRU9EdlRIdit5ZmlZVm8wckJLZE5mVTJhWGYzMGFKVlZvemZodHhaS2lvb3BKZXQ3SzNEcnVBazFBNjZLMGhjWDlubWZacng1NFk0TjBGc0xBc3VhUno5N0hrRzAzbi9jNnE1SXY3K1Z1UlAwdk1RMXVjOThKWWZ4UDEwMzI3YVhRbG1pSUVNZUZ3R2wxdUg0amdMYzcvRFNMaGVWNG5ub3pyeWZnNy9mczI3cXEiLCJtYWMiOiI1NDg4NTEyMTU4NDc4NGNhNDU4Mjg4MGYxOWEwNzUxMDJiMjQxNjJhNzY4OTI0NzVkNzc0NmEyZWQ2NTgyMmEyIiwidGFnIjoiIn0%3D; optional_info_open=false; ep_kontrak_session=eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IktxOC9XeFRPSVU0RWl4eEM2N0hVT0E9PSIsInZhbHVlIjoiTzlvcFJuODZXTStVdHRQd09QN3lDMy9Kb1dIekhVNjY4c3NkdTFNNXZFdFFCdlRxeEtRdmpweDZYRWt5TlBjcTdPakU4NEU0REw3VzFsWnNNUC93azZZUDRhdFlTUjUrMVF0TklyUUdUc1hobldra2crUEFYdmM0cDhCOVVvS1kiLCJtYWMiOiI3YmNjNzY1MGRmMmZmNTYyNDNmODQ0ZDViNjZiZWI3MzY1YzI0MGZlMzYwNTc1MTg2MmQ0NzJiYWQxNzNhMmU4IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImI1R3ZlWjFlcWRseFo2eUZvdDBQMGc9PSIsInZhbHVlIjoiR1hncC9NQms3b2RDVTQyUnNKai81eVJadkUxUE9OTUxFQ0VoR1ZOdGYyQmYxdm40N0FtSVM5RENMUXlxckRvcG5hV0FMeFphY3BlRFU1aGgzejVvcVA4Znk4ZGJta1c3WjBML0gyL0oxeEtRS1hPakFzb1A0ZFpLNDhUWk5Tc2siLCJtYWMiOiIwYWQ2YTEzZTUyZjE4Mjc0ZmY0NTQyMjM0YWMwNzZhNDcyMjZkYzY5N2ZhM2YxNWE4YTMzNDg3M2ZmYzg3OTBjIiwidGFnIjoiIn0%3D"]}, "controller": "App\\Livewire\\Project\\CRM", "getData": [], "postData": [], "requestData": "", "sessionData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "_previous": {"__type__": "array", "url": "http://localhost:8000/crm"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 4}, "authenticatedUser": {"id": 4, "username": "<EMAIL>", "email": "<EMAIL>", "name": "IQBAL FIKRI MOHAMED MISMAN ."}, "cookies": {"_ga": "GA1.1.913056526.1747301190", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "1751960299", "sugar_user_theme": "SuiteP", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0=", "snipeit_session": "fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm", "optional_user_info_open": "true", "snipeit_passport_token": "*removed*", "optional_info_open": "false", "ep_kontrak_session": "eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6IktxOC9XeFRPSVU0RWl4eEM2N0hVT0E9PSIsInZhbHVlIjoiTzlvcFJuODZXTStVdHRQd09QN3lDMy9Kb1dIekhVNjY4c3NkdTFNNXZFdFFCdlRxeEtRdmpweDZYRWt5TlBjcTdPakU4NEU0REw3VzFsWnNNUC93azZZUDRhdFlTUjUrMVF0TklyUUdUc1hobldra2crUEFYdmM0cDhCOVVvS1kiLCJtYWMiOiI3YmNjNzY1MGRmMmZmNTYyNDNmODQ0ZDViNjZiZWI3MzY1YzI0MGZlMzYwNTc1MTg2MmQ0NzJiYWQxNzNhMmU4IiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6ImI1R3ZlWjFlcWRseFo2eUZvdDBQMGc9PSIsInZhbHVlIjoiR1hncC9NQms3b2RDVTQyUnNKai81eVJadkUxUE9OTUxFQ0VoR1ZOdGYyQmYxdm40N0FtSVM5RENMUXlxckRvcG5hV0FMeFphY3BlRFU1aGgzejVvcVA4Znk4ZGJta1c3WjBML0gyL0oxeEtRS1hPakFzb1A0ZFpLNDhUWk5Tc2siLCJtYWMiOiIwYWQ2YTEzZTUyZjE4Mjc0ZmY0NTQyMjM0YWMwNzZhNDcyMjZkYzY5N2ZhM2YxNWE4YTMzNDg3M2ZmYzg3OTBjIiwidGFnIjoiIn0="}, "responseTime": 1754640601.397175, "responseStatus": 200, "responseDuration": 1675.9471893310547, "memoryUsage": 31457280, "middleware": ["web", "auth:web", "log.activity", "route.permission"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB' LIMIT 1", "duration": 48.34, "connection": "epss2", "time": 1754640600.397532, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 4 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 14.56, "connection": "epss2", "time": 1754640600.490978, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 9, "connection": "epss2", "time": 1754640600.527037, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 9.78, "connection": "epss2", "time": 1754640600.53738, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 8.87, "connection": "epss2", "time": 1754640600.5807092, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 8.93, "connection": "epss2", "time": 1754640600.592987, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 8.29, "connection": "epss2", "time": 1754640600.603736, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 10.23, "connection": "epss2", "time": 1754640600.614486, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 9.66, "connection": "epss2", "time": 1754640600.627096, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 13.01, "connection": "epss2", "time": 1754640600.640349, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 8.54, "connection": "epss2", "time": 1754640600.655123, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 15.23, "connection": "epss2", "time": 1754640600.664799, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 9.37, "connection": "epss2", "time": 1754640600.6816518, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 7.76, "connection": "epss2", "time": 1754640600.69183, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 15.42, "connection": "epss2", "time": 1754640600.70068, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 9.79, "connection": "epss2", "time": 1754640600.717372, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.69, "connection": "epss2", "time": 1754640600.7289119, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 8.09, "connection": "epss2", "time": 1754640600.738871, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 10.14, "connection": "epss2", "time": 1754640600.7480721, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.29, "connection": "epss2", "time": 1754640600.759362, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 13.81, "connection": "epss2", "time": 1754640600.7685921, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 12.02, "connection": "epss2", "time": 1754640600.7837138, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 9.03, "connection": "epss2", "time": 1754640600.798097, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 9.43, "connection": "epss2", "time": 1754640600.809526, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 10.26, "connection": "epss2", "time": 1754640600.8209908, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 8.03, "connection": "epss2", "time": 1754640600.832174, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.53, "connection": "epss2", "time": 1754640600.841587, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 12.78, "connection": "epss2", "time": 1754640600.851464, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 9.13, "connection": "epss2", "time": 1754640600.865545, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 13.19, "connection": "epss2", "time": 1754640600.876154, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 7.84, "connection": "epss2", "time": 1754640600.890741, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 8.65, "connection": "epss2", "time": 1754640600.899703, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.7, "connection": "epss2", "time": 1754640600.9091492, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 13.38, "connection": "epss2", "time": 1754640600.9185638, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8, "connection": "epss2", "time": 1754640600.9328191, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8.52, "connection": "epss2", "time": 1754640600.9417622, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 8.52, "connection": "epss2", "time": 1754640600.951466, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 8.42, "connection": "epss2", "time": 1754640600.961448, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 7.79, "connection": "epss2", "time": 1754640600.9706988, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 13.7, "connection": "epss2", "time": 1754640600.979188, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 14.77, "connection": "epss2", "time": 1754640600.993721, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.27, "connection": "epss2", "time": 1754640601.0092459, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 9.36, "connection": "epss2", "time": 1754640601.018324, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 9.96, "connection": "epss2", "time": 1754640601.029304, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 9.33, "connection": "epss2", "time": 1754640601.0412169, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.98, "connection": "epss2", "time": 1754640601.051795, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 14.02, "connection": "epss2", "time": 1754640601.0615711, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 12.2, "connection": "epss2", "time": 1754640601.076309, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 8.93, "connection": "epss2", "time": 1754640601.0893, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 8.6, "connection": "epss2", "time": 1754640601.099452, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.75, "connection": "epss2", "time": 1754640601.1103292, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.59, "connection": "epss2", "time": 1754640601.120387, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 8.52, "connection": "epss2", "time": 1754640601.131834, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 13.39, "connection": "epss2", "time": 1754640601.143194, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 9.63, "connection": "epss2", "time": 1754640601.159013, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 8.92, "connection": "epss2", "time": 1754640601.170341, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 14.74, "connection": "epss2", "time": 1754640601.181631, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 9.36, "connection": "epss2", "time": 1754640601.197813, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 15.05, "connection": "epss2", "time": 1754640601.208776, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 9.02, "connection": "epss2", "time": 1754640601.225775, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 9.29, "connection": "epss2", "time": 1754640601.236819, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 11.16, "connection": "epss2", "time": 1754640601.248439, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 10.23, "connection": "epss2", "time": 1754640601.2615, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 11.51, "connection": "epss2", "time": 1754640601.274805, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 13.41, "connection": "epss2", "time": 1754640601.2892869, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 9.26, "connection": "epss2", "time": 1754640601.305736, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 10.32, "connection": "epss2", "time": 1754640601.317729, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 11.27, "connection": "epss2", "time": 1754640601.330024, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoid2dBM1dyMlFMeklWUWgzRzFpYXU1aFpZZmVmeENpRmVpbGtrTHA3NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjU6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9jcm0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', `last_activity` = 1754640601, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB'", "duration": 11.85, "connection": "epss2", "time": 1754640601.384913, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 69, "databaseSlowQueries": 0, "databaseSelects": 68, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 750.4099999999996, "cacheQueries": [{"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640600.536598, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640600.547463, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640600.591265, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640600.602865, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640600.612599, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640600.625456, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640600.638369, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640600.654264, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640600.664084, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640600.680657, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640600.69135, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640600.70003, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640600.716427, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640600.727955, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640600.738068, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640600.747358, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640600.758787, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640600.768005, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640600.782769, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640600.796636, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640600.808354, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640600.81988, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640600.831636, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640600.840608, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640600.85088, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640600.864533, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640600.875547, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640600.889794, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640600.899187, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640600.908634, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640600.918128, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640600.932318, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640600.941248, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640600.95061, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640600.960804, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640600.970177, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640600.978761, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640600.993171, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640601.008801, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640601.017817, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640601.027972, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640601.040345, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640601.051179, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640601.061053, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640601.07588, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640601.088819, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640601.098523, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640601.108868, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640601.119688, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640601.129917, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640601.141825, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640601.157985, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640601.169011, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640601.180041, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640601.196767, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640601.207538, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640601.224819, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640601.235282, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640601.247438, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640601.260038, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640601.272974, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640601.287575, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640601.304003, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640601.316272, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640601.329104, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640601.341678, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}], "cacheReads": 66, "cacheHits": 66, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754640600.359184, "end": 1754640601.397115, "duration": 1037.930965423584, "color": null, "data": null}], "log": [{"message": "User Activity", "exception": null, "context": {"__type__": "array", "user_id": 4, "name": "IQBAL FIKRI MOHAMED MISMAN .", "email": "<EMAIL>", "url": "http://localhost:8000/crm", "method": "GET", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "level": "info", "time": 1754640600.520367, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 16, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "POST", "uri": "download", "name": "download", "action": "App\\Http\\Controllers\\ExportController@downloadUom", "middleware": ["web"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1754640600.560704, "end": 1754640600.560704, "duration": 0, "color": null, "data": {"name": "livewire.project.crm.index", "data": {"__type__": "array", "activeTab": null}}}, {"description": "Rendering a view", "start": 1754640600.566341, "end": 1754640600.566341, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;G9rN7ffGpwOSDge8we5r&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b44bc1a1c8575a869f99fd3814b5118587a3c0249f7c079cd06bc9782a984054&quot;}\" wire:effects=\"[]\" wire:id=\"G9rN7ffGpwOSDge8we5r\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.app", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1754640600.571174, "end": 1754640600.571174, "duration": 0, "color": null, "data": {"name": "layouts.app", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;G9rN7ffGpwOSDge8we5r&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b44bc1a1c8575a869f99fd3814b5118587a3c0249f7c079cd06bc9782a984054&quot;}\" wire:effects=\"[]\" wire:id=\"G9rN7ffGpwOSDge8we5r\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}, {"description": "Rendering a view", "start": 1754640601.343091, "end": 1754640601.343091, "duration": 0, "color": null, "data": {"name": "livewire.component.sidebar", "data": {"__type__": "array", "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1754640601.345538, "end": 1754640601.345538, "duration": 0, "color": null, "data": {"name": "partials.sidebar.header", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "GHQQrlplRL48DLhVgMag", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1754640601.347831, "end": 1754640601.347831, "duration": 0, "color": null, "data": {"name": "partials.sidebar.user-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "GHQQrlplRL48DLhVgMag", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}, "loop": null}}}, {"description": "Rendering a view", "start": 1754640601.350573, "end": 1754640601.350573, "duration": 0, "color": null, "data": {"name": "partials.sidebar.item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "GHQQrlplRL48DLhVgMag", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}, "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}}, {"description": "Rendering a view", "start": 1754640601.353336, "end": 1754640601.353336, "duration": 0, "color": null, "data": {"name": "partials.sidebar.simple-item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "GHQQrlplRL48DLhVgMag", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 2, "count": 3, "first": true, "last": false, "odd": true, "even": false, "depth": 2, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}}, "type": "item", "title": "CRM - CS Cases", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}, "level": 2, "linkPadding": "ps-2.5", "levelClasses": "", "item": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "disabled": false, "badge": null}}}, {"description": "Rendering a view", "start": 1754640601.368152, "end": 1754640601.368152, "duration": 0, "color": null, "data": {"name": "livewire.component.header", "data": {"__type__": "array", "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}}}}}, {"description": "Rendering a view", "start": 1754640601.370654, "end": 1754640601.370654, "duration": 0, "color": null, "data": {"name": "components.footer", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array"}}, "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;G9rN7ffGpwOSDge8we5r&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b44bc1a1c8575a869f99fd3814b5118587a3c0249f7c079cd06bc9782a984054&quot;}\" wire:effects=\"[]\" wire:id=\"G9rN7ffGpwOSDge8we5r\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "92cf3473"}