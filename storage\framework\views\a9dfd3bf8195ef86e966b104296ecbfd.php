<div wire:replace class="">
    <div class="grid gap-5 lg:gap-7.5">
        <div class="py-12 bg-white text-gray-900 dark:bg-coal-600">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i><strong>POMS Talkdesk</strong> Data Upload <br>
                    <small class="text-info">Please only upload a <span><strong>.CSV</strong></span> file, and kindly
                        ensure
                        that
                        you retain its original file name.</small>
                </h1>
            </div>
        </div>

        
        <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
            <div class="alert alert-success">
                <?php echo e(session('success')); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <?php if(session()->has('warning')): ?>
            <div class="alert alert-warning">
                <?php echo e(session('warning')); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <?php if(session()->has('error')): ?>
            <div class="alert alert-danger">
                <?php echo e(session('error')); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <div class="block">
            <div class="block-title">
                <h2>Upload <strong>Talkdesk</strong> Call Data File</h2>
            </div>

            <?php echo $__env->make('partials.crm.poms-talkdesk.type-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <?php echo $__env->make('partials.crm.poms-talkdesk.upload-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <div style="zoom: 0.8;">
                <?php echo $__env->make('partials.crm.poms-talkdesk.poms-talkdesk-datatable', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

        </div>
    </div>
</div>

    <?php
        $__scriptKey = '2494406807-0';
        ob_start();
    ?>
<script>
    Livewire.on('refresh-talkdesk-table', function () {
        setTimeout(() => {
            initializeTalkdeskDatatable();
        }, 0);
    });

    function initializeTalkdeskDatatable() {
        const tableContainer = document.querySelector('#talkdesk-data-table');
        if (tableContainer) {
            try {
                const options = {
                    pageSize: 10,
                    pageSizes: [5, 10, 20, 50],
                    stateSave: false
                };

                const tableElement = tableContainer.querySelector('[data-datatable-table="true"]');
                if (tableElement) {
                    const existingDatatable = KTDataTable.getInstance(tableElement);
                    if (existingDatatable) {
                        existingDatatable.refresh();
                    } else {
                        new KTDataTable(tableContainer, options);
                    }
                }
            } catch (error) {
                console.error("Error initializing talkdesk datatable:", error);
            }
        }
    }
</script>
    <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?><?php /**PATH E:\workspace\epss-revamp\resources\views/livewire/crm/poms-talkdesk.blade.php ENDPATH**/ ?>