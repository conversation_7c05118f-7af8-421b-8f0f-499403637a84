<form wire:submit="uploadData">
    <div wire:loading.remove wire:target="loadTalkdeskData" x-data="{ uploading: false, progress: 0 }"
        x-on:livewire-upload-start="uploading = true" x-on:livewire-upload-finish="uploading = false; progress = 100"
        x-on:livewire-upload-cancel="uploading = false; progress = 0"
        x-on:livewire-upload-error="uploading = false; progress = 0"
        x-on:livewire-upload-progress="progress = $event.detail.progress">

        <div class="form-group mb-3">
            <label class="mb-2">Upload CSV File for <?php echo e(ucfirst($typeForm->type)); ?> Data:</label>

            <div class="mb-3">
                <input wire:model.live="uploadForm.uploadFile" type="file" accept=".csv" class="file-input"
                    id="file-upload">
            </div>

            
            <!--[if BLOCK]><![endif]--><?php if($selectedFileName): ?>
                <div class="mb-3 p-3 bg-gray-50 rounded border">
                    <div class="flex items-center">
                        <i class="fa fa-file-csv text-green-600 mr-2"></i>
                        <div>
                            <strong>Selected File:</strong> <?php echo e($selectedFileName); ?><br>
                            <small class="text-gray-600">Size: <?php echo e($selectedFileSize); ?></small>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            
            <div x-show="uploading" class="mb-3">
                <div class="mb-2">
                    <span class="text-sm font-medium">Processing...</span>
                    <span class="text-sm text-gray-600" x-text="progress + '%'"></span>
                </div>
                <progress max="100" x-bind:value="progress" class="w-full h-2"></progress>
            </div>

            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['uploadForm.uploadFile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-red-600 mt-2 block"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!--[if BLOCK]><![endif]--><?php if($selectedFileName): ?>
            <button type="submit" class="btn btn-primary">
                <span>
                    Upload <?php echo e(ucfirst($typeForm->type)); ?> File
                </span>
                <i wire:loading wire:target="uploadData" class="ki-filled ki-loading animate-spin"></i>
            </button>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <button x-show="uploading" type="button" class="btn btn-secondary"
            wire:click="$cancelUpload('uploadForm.uploadFile')">Cancel Upload</button>
    </div>
</form><?php /**PATH E:\workspace\epss-revamp\resources\views/partials/crm/poms-talkdesk/upload-form.blade.php ENDPATH**/ ?>