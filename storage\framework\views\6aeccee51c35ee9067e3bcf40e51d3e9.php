<form wire:submit="uploadData">
    <div
        wire:loading.remove
        wire:target="loadTalkdeskData"
        x-data="{
            uploading: false,
            progress: 0,
            dropActive: false,
            setFiles(files){
                if(!files || files.length === 0) return;
                // Use DataTransfer to set input files for Livewire
                const dt = new DataTransfer();
                dt.items.add(files[0]);
                this.$refs.fileInput.files = dt.files;
                this.$refs.fileInput.dispatchEvent(new Event('change'));
            }
        }"
        x-on:livewire-upload-start="uploading = true"
        x-on:livewire-upload-finish="uploading = false; progress = 100"
        x-on:livewire-upload-cancel="uploading = false; progress = 0"
        x-on:livewire-upload-error="uploading = false; progress = 0"
        x-on:livewire-upload-progress="progress = $event.detail.progress"
    >
        <div class="flex flex-col gap-4">
            <label class="form-label">Upload CSV File for <?php echo e(ucfirst($typeForm->type)); ?> data</label>

            <!-- Drag & drop area -->
            <div
                class="flex flex-col items-center justify-center text-center gap-2.5 p-8 border-2 border-dashed rounded-lg cursor-pointer transition bg-light hover:bg-light-active border-gray-300"
                :class="dropActive ? 'border-primary text-primary' : 'text-gray-700'"
                @click.prevent="$refs.fileInput.click()"
                @dragover.prevent="dropActive = true"
                @dragleave.prevent="dropActive = false"
                @drop.prevent="dropActive = false; setFiles($event.dataTransfer.files)"
            >
                <i class="ki-filled ki-cloud-upload text-3xl"></i>
                <div class="text-sm">
                    <span class="font-medium">Drag & drop your .csv here</span>
                    <span class="text-gray-500"> or click to browse</span>
                </div>
                <div class="text-2xs text-gray-500">Only .csv files are accepted. Max 10MB.</div>
            </div>

            <!-- Hidden/native input (kept for accessibility) -->
            <div class="flex items-center gap-3">
                <input
                    x-ref="fileInput"
                    id="file-upload"
                    class="file-input max-w-sm"
                    type="file"
                    accept=".csv"
                    wire:model.live="uploadForm.uploadFile"
                />
                <button type="button" class="btn btn-light" @click="$refs.fileInput.click()">
                    <i class="ki-filled ki-file"></i>
                    Browse
                </button>
            </div>

            <!-- Selected File Info -->
            <!--[if BLOCK]><![endif]--><?php if($selectedFileName): ?>
                <div class="p-3 bg-gray-50 rounded border">
                    <div class="flex items-center gap-2">
                        <i class="ki-filled ki-file-added text-success"></i>
                        <div class="text-sm">
                            <strong>Selected:</strong> <?php echo e($selectedFileName); ?>

                            <span class="text-gray-600">&nbsp;• <?php echo e($selectedFileSize); ?></span>
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Upload Progress Bar -->
            <div x-show="uploading" class="space-y-2">
                <div class="flex items-center justify-between text-sm">
                    <span class="font-medium">Uploading...</span>
                    <span class="text-gray-600" x-text="progress + '%'" ></span>
                </div>
                <div class="h-2 w-full bg-gray-200 rounded">
                    <div class="h-2 bg-primary rounded" :style="`width: ${progress}%;`"></div>
                </div>
            </div>

            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['uploadForm.uploadFile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="text-danger mt-2 block"><?php echo e($message); ?></span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Actions -->
            <div class="flex items-center justify-end gap-2 pt-2">
                <button x-show="uploading" type="button" class="btn btn-secondary"
                        wire:click="$cancelUpload('uploadForm.uploadFile')">Cancel</button>
                <!--[if BLOCK]><![endif]--><?php if($selectedFileName): ?>
                    <button type="submit" class="btn btn-primary">
                        <i class="ki-filled ki-upload"></i>
                        Upload <?php echo e(ucfirst($typeForm->type)); ?> File
                        <i wire:loading wire:target="uploadData" class="ki-filled ki-loading animate-spin"></i>
                    </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
</form>

<?php /**PATH E:\workspace\epss-revamp\resources\views/partials/crm/poms-talkdesk/upload-form-dnd.blade.php ENDPATH**/ ?>