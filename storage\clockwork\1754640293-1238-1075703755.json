{"id": "1754640293-1238-1075703755", "version": 1, "type": "request", "time": 1754640292.926633, "method": "GET", "url": "http://localhost:8000/crm", "uri": "/crm", "headers": {"host": ["localhost:8000"], "connection": ["keep-alive"], "sec-ch-ua": ["\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""], "sec-ch-ua-mobile": ["?0"], "sec-ch-ua-platform": ["\"Windows\""], "upgrade-insecure-requests": ["1"], "user-agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"], "accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"], "sec-fetch-site": ["same-origin"], "sec-fetch-mode": ["navigate"], "sec-fetch-user": ["?1"], "sec-fetch-dest": ["document"], "referer": ["http://localhost:8000/home"], "accept-encoding": ["gzip, deflate, br, zstd"], "accept-language": ["en-GB,en-US;q=0.9,en;q=0.8,ms;q=0.7"], "cookie": ["_ga=GA1.1.913056526.1747301190; Cases_divs=a%3Dnone%23h%3D%23n%3D%23; ck_login_id_20=1; ck_login_language_20=en_us; _ga_8PH6FM2JEL=GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0; wp-settings-1=libraryContent%3Dbrowse%26uploader%3D1%26editor%3Dhtml; wp-settings-time-1=1751960299; sugar_user_theme=SuiteP; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0%3D; snipeit_session=fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm; optional_user_info_open=true; snipeit_passport_token=eyJpdiI6IlQrMExxNDVtYWlGQlJ4YTRzcEQwZnc9PSIsInZhbHVlIjoiclo3MFpxRGlCbWhxRmJPOGhWQzNRYTN5Rlk5VmNlZDY2Sm1wczgxRFRzajI0SVBIaGhzQWs0aDBjYWFubndTMXJDUkd4YUZLbFFFMUo0NmZSa3pWRkl6SnJDOW1Hc2w1dXY2dzE3QTUrV1owVVlnU01IS0pIeDdyOGhtTjVtTjZCLzlNSmVYZE1LRG13amdJOC9MRU9EdlRIdit5ZmlZVm8wckJLZE5mVTJhWGYzMGFKVlZvemZodHhaS2lvb3BKZXQ3SzNEcnVBazFBNjZLMGhjWDlubWZacng1NFk0TjBGc0xBc3VhUno5N0hrRzAzbi9jNnE1SXY3K1Z1UlAwdk1RMXVjOThKWWZ4UDEwMzI3YVhRbG1pSUVNZUZ3R2wxdUg0amdMYzcvRFNMaGVWNG5ub3pyeWZnNy9mczI3cXEiLCJtYWMiOiI1NDg4NTEyMTU4NDc4NGNhNDU4Mjg4MGYxOWEwNzUxMDJiMjQxNjJhNzY4OTI0NzVkNzc0NmEyZWQ2NTgyMmEyIiwidGFnIjoiIn0%3D; optional_info_open=false; ep_kontrak_session=eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjBVejNMSDhUQzZYdEFsWG13TjlTNmc9PSIsInZhbHVlIjoiT0FaMDBDNzFCKzVTOFNPcWE4eng2cE13MzgyOFU4NVVBWnN0NTVXWWI3V1JQZjVwQkUvaGpYQWRiQmFQVUprYndEQkU1b3RpanZ1OXM1ZCtmMHZRcVBoOTRVZ25IUXpFdklMOWlwWXkvdk5Qb0ZtOFcyWnYvcjA3bWs4VGxPaE4iLCJtYWMiOiIxOTJhMzVhY2I5NTQ1ZmI5ZjdmNWFjMTM3MDM2NTgwZTdkYjNiMzhhYjFiNDhmYmY2YjhmZjEzYmE2MDE4NjhkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZ6UktScGkza1FrS29LREhsNmpoUXc9PSIsInZhbHVlIjoiOE1nZEQ2enZVUVArcWY3U3FBQkN0Ym9nblpURmlPTmVVVS9xbnpsOEpsVzZFSlJjdTRLN0orVUFSalVWKzVFNXh2b1VvNnBBQk5FNTFidVF2UXQyczFUSXNqQy80WG50dHBJSkN3NmYwdG1zRk1GMmFkYlJKYVhNNUFPZFpESUQiLCJtYWMiOiIxNjFmMmMxY2Y0ZWZmNzNiNTUyYTY3NmFmNTBhMDFhZjRiYWZlODEzOGIwZmM5ODMyNzk0NjdiNjRiYTgxNjIzIiwidGFnIjoiIn0%3D; x-clockwork=%7B%22requestId%22%3A%221754640279-2906-1904907473%22%2C%22version%22%3A%225.3.4%22%2C%22path%22%3A%22%5C%2F__clockwork%5C%2F%22%2C%22webPath%22%3A%22%5C%2Fclockwork%5C%2Fapp%22%2C%22token%22%3A%2282551c00%22%2C%22metrics%22%3Atrue%2C%22toolbar%22%3Atrue%7D"]}, "controller": "App\\Livewire\\Project\\CRM", "getData": [], "postData": [], "requestData": "", "sessionData": {"_token": "wgA3Wr2QLzIVQh3G1iau5hZYfefxCiFeilkkLp74", "_previous": {"__type__": "array", "url": "http://localhost:8000/crm"}, "_flash": {"__type__": "array", "old": {"__type__": "array"}, "new": {"__type__": "array"}}, "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": 4}, "authenticatedUser": {"id": 4, "username": "<EMAIL>", "email": "<EMAIL>", "name": "IQBAL FIKRI MOHAMED MISMAN ."}, "cookies": {"_ga": "GA1.1.913056526.1747301190", "Cases_divs": "a=none#h=#n=#", "ck_login_id_20": "1", "ck_login_language_20": "en_us", "_ga_8PH6FM2JEL": "GS2.1.s1751619462$o19$g1$t1751620652$j60$l0$h0", "wp-settings-1": "libraryContent=browse&uploader=1&editor=html", "wp-settings-time-1": "1751960299", "sugar_user_theme": "SuiteP", "remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "eyJpdiI6IlRSeEtreCs4QUFpZVh2clIrZHRHV3c9PSIsInZhbHVlIjoieU55cXlwVTk3TUx2Z1o3aFVSQVFncy8yWi9WUU8vT0M4VUgrelhNeU5iTmRicEZ6bkhSc0tDa3Bibk81OGUxMS9xZGREWXNrSkFrSHpad0ltWTgyMnU3Y2ZLN2VsK0dIeG5WKzRNeE9CRDdNMmhmUU9vakpWUEZ6QzB1SVE4b2VRMTErallCUmR0bndGY1lWbSthQW10Q0lweFR4YTVRaG1MZWdRUWFxT0lrd1JSK2x5cWMrUFR5aitTdE5TVzRJMEwybjhWc0ozSkhic0FoRWZVM2VsVlgrK0FCeGdQQlVCNFFWQmUrOC83QT0iLCJtYWMiOiJmOWEwNzc4ODdlN2RiOTU3YWQ5OTEyMWRiNTJlZmU4NGJmNzUyODczMjc5OWJlMWY1OGUzNmEzOTk1NjljODc0IiwidGFnIjoiIn0=", "snipeit_session": "fYryN54L6soRsZ1rbiJLCIqY63SFJLN7k67dZPtm", "optional_user_info_open": "true", "snipeit_passport_token": "*removed*", "optional_info_open": "false", "ep_kontrak_session": "eyJpdiI6InJ3UUcvNnRGWjhLT1BVSGV4eWp0OXc9PSIsInZhbHVlIjoiVDE3ZkpwSkVNdUQ1alpDWWVYQ0FsclVSdWVMMnI1clM2RjlTb3ZLek1McFFoR3UvZFpFNVE2MHYzY3NqT2FpZjl3ZUNCRG83L3VBVWpZMVhVbkVhNWh5MzcxdDhaUHYyM29ySk5RTUlpUmZSZC8vTFFLdzJ4ZE05ZzBkU3ZwL3MiLCJtYWMiOiJlMjcyNDMwNWRiOGM2MDY3Y2IwYTUxODY2NDFiMTk0Yjk0YTM1NDFjMTdmYTdiY2I3ZWZlM2Y2ZjVkZDZlOGIyIiwidGFnIjoiIn0=", "XSRF-TOKEN": "eyJpdiI6IjBVejNMSDhUQzZYdEFsWG13TjlTNmc9PSIsInZhbHVlIjoiT0FaMDBDNzFCKzVTOFNPcWE4eng2cE13MzgyOFU4NVVBWnN0NTVXWWI3V1JQZjVwQkUvaGpYQWRiQmFQVUprYndEQkU1b3RpanZ1OXM1ZCtmMHZRcVBoOTRVZ25IUXpFdklMOWlwWXkvdk5Qb0ZtOFcyWnYvcjA3bWs4VGxPaE4iLCJtYWMiOiIxOTJhMzVhY2I5NTQ1ZmI5ZjdmNWFjMTM3MDM2NTgwZTdkYjNiMzhhYjFiNDhmYmY2YjhmZjEzYmE2MDE4NjhkIiwidGFnIjoiIn0=", "laravel_session": "eyJpdiI6IkZ6UktScGkza1FrS29LREhsNmpoUXc9PSIsInZhbHVlIjoiOE1nZEQ2enZVUVArcWY3U3FBQkN0Ym9nblpURmlPTmVVVS9xbnpsOEpsVzZFSlJjdTRLN0orVUFSalVWKzVFNXh2b1VvNnBBQk5FNTFidVF2UXQyczFUSXNqQy80WG50dHBJSkN3NmYwdG1zRk1GMmFkYlJKYVhNNUFPZFpESUQiLCJtYWMiOiIxNjFmMmMxY2Y0ZWZmNzNiNTUyYTY3NmFmNTBhMDFhZjRiYWZlODEzOGIwZmM5ODMyNzk0NjdiNjRiYTgxNjIzIiwidGFnIjoiIn0=", "x-clockwork": "{\"requestId\":\"1754640279-2906-1904907473\",\"version\":\"5.3.4\",\"path\":\"\\/__clockwork\\/\",\"webPath\":\"\\/clockwork\\/app\",\"token\":\"82551c00\",\"metrics\":true,\"toolbar\":true}"}, "responseTime": 1754640294.253411, "responseStatus": 200, "responseDuration": 1326.7781734466553, "memoryUsage": 29360128, "middleware": ["web", "auth:web", "log.activity", "route.permission"], "databaseQueries": [{"query": "SELECT * FROM `sessions` WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB' LIMIT 1", "duration": 52.13, "connection": "epss2", "time": 1754640293.310925, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `users` WHERE `id` = 4 and `users`.`deleted_at` IS NULL LIMIT 1", "duration": 9.28, "connection": "epss2", "time": 1754640293.388207, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": "App\\Models\\User", "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 8.83, "connection": "epss2", "time": 1754640293.411231, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 9.74, "connection": "epss2", "time": 1754640293.421157, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 14.5, "connection": "epss2", "time": 1754640293.457225, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm_action_read')", "duration": 18.17, "connection": "epss2", "time": 1754640293.473059, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 8.46, "connection": "epss2", "time": 1754640293.49255, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-cases_action_read')", "duration": 8.82, "connection": "epss2", "time": 1754640293.502406, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 8.38, "connection": "epss2", "time": 1754640293.512628, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.cs-pending-input_action_read')", "duration": 8.41, "connection": "epss2", "time": 1754640293.5222461, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 7.79, "connection": "epss2", "time": 1754640293.531867, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_crm.poms-talkdesk_action_read')", "duration": 10.58, "connection": "epss2", "time": 1754640293.541075, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 8.16, "connection": "epss2", "time": 1754640293.553509, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep_action_read')", "duration": 15.79, "connection": "epss2", "time": 1754640293.563286, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 10.5, "connection": "epss2", "time": 1754640293.58075, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.dashboard_action_read')", "duration": 9.18, "connection": "epss2", "time": 1754640293.592711, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 9.2, "connection": "epss2", "time": 1754640293.603554, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.qt.summary_action_read')", "duration": 9.18, "connection": "epss2", "time": 1754640293.613931, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.57, "connection": "epss2", "time": 1754640293.6244001, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.my-identity_action_read')", "duration": 8.82, "connection": "epss2", "time": 1754640293.634605, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 11.74, "connection": "epss2", "time": 1754640293.645404, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.user-login_action_read')", "duration": 9.64, "connection": "epss2", "time": 1754640293.658133, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 17.02, "connection": "epss2", "time": 1754640293.6694, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.profile.organization_action_read')", "duration": 9.5, "connection": "epss2", "time": 1754640293.6877549, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 8.15, "connection": "epss2", "time": 1754640293.698704, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-technical_action_read')", "duration": 9.42, "connection": "epss2", "time": 1754640293.7082882, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.71, "connection": "epss2", "time": 1754640293.719125, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.supplier-cs_action_read')", "duration": 8.95, "connection": "epss2", "time": 1754640293.729193, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 9.24, "connection": "epss2", "time": 1754640293.739563, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.tracking-diary_action_read')", "duration": 8.12, "connection": "epss2", "time": 1754640293.750377, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 8.43, "connection": "epss2", "time": 1754640293.759994, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.uom_action_read')", "duration": 12.44, "connection": "epss2", "time": 1754640293.7694201, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 9, "connection": "epss2", "time": 1754640293.782877, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-code_action_read')", "duration": 8.78, "connection": "epss2", "time": 1754640293.793161, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 8.85, "connection": "epss2", "time": 1754640293.803226, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.unspsc-item_action_read')", "duration": 12.45, "connection": "epss2", "time": 1754640293.813797, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 13.17, "connection": "epss2", "time": 1754640293.828078, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.supplier-item_action_read')", "duration": 9.75, "connection": "epss2", "time": 1754640293.843189, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 8.93, "connection": "epss2", "time": 1754640293.855112, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_ep.item.item-task-history_action_read')", "duration": 14.68, "connection": "epss2", "time": 1754640293.86608, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.66, "connection": "epss2", "time": 1754640293.882598, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report_action_read')", "duration": 8.06, "connection": "epss2", "time": 1754640293.892803, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 8.68, "connection": "epss2", "time": 1754640293.902145, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.today-trans_action_read')", "duration": 10.68, "connection": "epss2", "time": 1754640293.912699, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.6, "connection": "epss2", "time": 1754640293.9252, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.accumulative_action_read')", "duration": 8.83, "connection": "epss2", "time": 1754640293.9354448, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 10.33, "connection": "epss2", "time": 1754640293.946788, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.daily-summary_action_read')", "duration": 8.36, "connection": "epss2", "time": 1754640293.9594262, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 13.41, "connection": "epss2", "time": 1754640293.970418, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_report.revenue-report.pending-transaction_action_read')", "duration": 8.51, "connection": "epss2", "time": 1754640293.986009, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 9.34, "connection": "epss2", "time": 1754640293.9961228, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm_action_read')", "duration": 8.84, "connection": "epss2", "time": 1754640294.007375, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 8.53, "connection": "epss2", "time": 1754640294.018538, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.find-task_action_read')", "duration": 9.46, "connection": "epss2", "time": 1754640294.028731, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 8.21, "connection": "epss2", "time": 1754640294.0397692, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.instance-query_action_read')", "duration": 10, "connection": "epss2", "time": 1754640294.05009, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 8.83, "connection": "epss2", "time": 1754640294.0620248, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.process-manager_action_read')", "duration": 12.78, "connection": "epss2", "time": 1754640294.072352, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 8.09, "connection": "epss2", "time": 1754640294.086593, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.worklist-manager_action_read')", "duration": 8.54, "connection": "epss2", "time": 1754640294.0965881, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 8.84, "connection": "epss2", "time": 1754640294.108367, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.service-manager_action_read')", "duration": 9.55, "connection": "epss2", "time": 1754640294.1188788, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 8.35, "connection": "epss2", "time": 1754640294.130516, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.error-handler_action_read')", "duration": 9.56, "connection": "epss2", "time": 1754640294.1407318, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 9.88, "connection": "epss2", "time": 1754640294.152956, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_bpm.bpm-api.component-instance_action_read')", "duration": 8.92, "connection": "epss2", "time": 1754640294.165527, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 13.69, "connection": "epss2", "time": 1754640294.1773, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "SELECT * FROM `cache` WHERE `key` in ('laravel_cache_user_4_route_settings_action_read')", "duration": 8.64, "connection": "epss2", "time": 1754640294.193492, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}], "model": null, "tags": []}, {"query": "UPDATE `sessions` SET `payload` = 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoid2dBM1dyMlFMeklWUWgzRzFpYXU1aFpZZmVmeENpRmVpbGtrTHA3NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjU6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9jcm0iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', `last_activity` = 1754640294, `user_id` = 4, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' WHERE `id` = 'tSBgtIrZ7Vt0ShzkaS4efzmMARhACvTHNnmVwLvB'", "duration": 10.79, "connection": "epss2", "time": 1754640294.241977, "trace": [{"call": "Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php", "line": 19, "isVendor": true}, {"call": "Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 51, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php", "line": 27, "isVendor": true}, {"call": "Illuminate\\Http\\Middleware\\ValidatePostSize->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}], "model": null, "tags": []}], "databaseQueriesCount": 69, "databaseSlowQueries": 0, "databaseSelects": 68, "databaseInserts": 0, "databaseUpdates": 1, "databaseDeletes": 0, "databaseOthers": 0, "databaseDuration": 726.42, "cacheQueries": [{"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640293.42056, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640293.431315, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\CheckRoutePermission.php", "line": 39, "isVendor": false}, {"call": "App\\Http\\Middleware\\CheckRoutePermission->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 26, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640293.472355, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm_action_read", "expiration": null, "time": 1754640293.49166, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640293.501556, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-cases_action_read", "expiration": null, "time": 1754640293.511761, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640293.521569, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.cs-pending-input_action_read", "expiration": null, "time": 1754640293.531027, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640293.540203, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_crm.poms-talkdesk_action_read", "expiration": null, "time": 1754640293.552269, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640293.56238, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep_action_read", "expiration": null, "time": 1754640293.579702, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640293.591757, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.dashboard_action_read", "expiration": null, "time": 1754640293.602487, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640293.613323, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.qt.summary_action_read", "expiration": null, "time": 1754640293.623455, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640293.633556, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.my-identity_action_read", "expiration": null, "time": 1754640293.644314, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640293.657497, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.user-login_action_read", "expiration": null, "time": 1754640293.668351, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640293.687072, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.profile.organization_action_read", "expiration": null, "time": 1754640293.697589, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640293.707531, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-technical_action_read", "expiration": null, "time": 1754640293.718066, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640293.728445, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.supplier-cs_action_read", "expiration": null, "time": 1754640293.738603, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640293.749467, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.tracking-diary_action_read", "expiration": null, "time": 1754640293.75902, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640293.768909, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.uom_action_read", "expiration": null, "time": 1754640293.782135, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640293.792519, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-code_action_read", "expiration": null, "time": 1754640293.802253, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640293.812731, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.unspsc-item_action_read", "expiration": null, "time": 1754640293.826882, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640293.842057, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.supplier-item_action_read", "expiration": null, "time": 1754640293.853657, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640293.864961, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_ep.item.item-task-history_action_read", "expiration": null, "time": 1754640293.881298, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640293.891956, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report_action_read", "expiration": null, "time": 1754640293.901355, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640293.911636, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.today-trans_action_read", "expiration": null, "time": 1754640293.924019, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640293.934449, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.accumulative_action_read", "expiration": null, "time": 1754640293.945319, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640293.958053, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.daily-summary_action_read", "expiration": null, "time": 1754640293.968753, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640293.984972, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_report.revenue-report.pending-transaction_action_read", "expiration": null, "time": 1754640293.995153, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640294.006366, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm_action_read", "expiration": null, "time": 1754640294.017076, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640294.027808, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.find-task_action_read", "expiration": null, "time": 1754640294.03877, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640294.048643, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.instance-query_action_read", "expiration": null, "time": 1754640294.060716, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640294.071426, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.process-manager_action_read", "expiration": null, "time": 1754640294.085685, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640294.095212, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.worklist-manager_action_read", "expiration": null, "time": 1754640294.106397, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640294.118028, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.service-manager_action_read", "expiration": null, "time": 1754640294.128999, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640294.139591, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.error-handler_action_read", "expiration": null, "time": 1754640294.151026, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640294.164177, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_bpm.bpm-api.component-instance_action_read", "expiration": null, "time": 1754640294.175443, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 90, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640294.192165, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 162, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}, {"type": "hit", "key": "user_4_route_settings_action_read", "expiration": null, "time": 1754640294.202922, "connection": null, "trace": [{"call": "Illuminate\\Cache\\CacheManager->__call()", "file": "E:\\workspace\\epss-revamp\\app\\Models\\User.php", "line": 163, "isVendor": false}, {"call": "App\\Models\\User->canAccessRoute()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 37, "isVendor": false}, {"call": "App\\Services\\MenuService->processMenuItem()", "file": "E:\\workspace\\epss-revamp\\app\\Services\\MenuService.php", "line": 25, "isVendor": false}, {"call": "App\\Services\\MenuService->getMenu()", "file": "E:\\workspace\\epss-revamp\\app\\Livewire\\Component\\Sidebar.php", "line": 34, "isVendor": false}, {"call": "App\\Livewire\\Component\\Sidebar->mount()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43, "isVendor": true}, {"call": "Illuminate\\Container\\Util::unwrapIfClosure()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::callBoundMethod()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35, "isVendor": true}, {"call": "Illuminate\\Container\\BoundMethod::call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Wrapped.php", "line": 23, "isVendor": true}, {"call": "Livewire\\Wrapped->__call()", "file": "E:\\workspace\\epss-revamp\\vendor\\livewire\\livewire\\src\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks.php", "line": 134, "isVendor": true}]}], "cacheReads": 66, "cacheHits": 66, "cacheWrites": 0, "cacheDeletes": 0, "cacheTime": 0, "modelsActions": [], "modelsRetrieved": {"App\\Models\\User": 1}, "modelsCreated": [], "modelsUpdated": [], "modelsDeleted": [], "redisCommands": [], "queueJobs": [], "timelineData": [{"description": "Controller", "start": 1754640293.284118, "end": 1754640294.253301, "duration": 969.1829681396484, "color": null, "data": null}], "log": [{"message": "User Activity", "exception": null, "context": {"__type__": "array", "user_id": 4, "name": "IQBAL FIKRI MOHAMED MISMAN .", "email": "<EMAIL>", "url": "http://localhost:8000/crm", "method": "GET", "ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, "level": "info", "time": 1754640293.407011, "trace": [{"call": "Illuminate\\Support\\Facades\\Facade::__callStatic()", "file": "E:\\workspace\\epss-revamp\\app\\Http\\Middleware\\LogUserActivity.php", "line": 16, "isVendor": false}, {"call": "App\\Http\\Middleware\\LogUserActivity->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50, "isVendor": true}, {"call": "Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63, "isVendor": true}, {"call": "Illuminate\\Auth\\Middleware\\Authenticate->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87, "isVendor": true}, {"call": "Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}, {"call": "Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 48, "isVendor": true}, {"call": "Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()", "file": "E:\\workspace\\epss-revamp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208, "isVendor": true}]}], "events": [], "routes": [{"method": "GET, HEAD", "uri": "login", "name": "login", "action": "App\\Livewire\\Login", "middleware": ["web"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "home", "name": "home", "action": "App\\Livewire\\HomePage", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "profile", "name": "profile", "action": "App\\Livewire\\UserProfile", "middleware": ["web", "auth:web", "log.activity"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "crm", "name": "crm", "action": "App\\Livewire\\Project\\CRM", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "ep", "name": "ep", "action": "App\\Livewire\\Project\\EPerolehan", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "report", "name": "report", "action": "App\\Livewire\\Project\\Report", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "bpm", "name": "bpm", "action": "App\\Livewire\\Project\\Bpm", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "GET, HEAD", "uri": "settings", "name": "settings", "action": "App\\Livewire\\AppSetting", "middleware": ["web", "auth:web", "log.activity", "route.permission"], "before": "", "after": ""}, {"method": "POST", "uri": "download", "name": "download", "action": "App\\Http\\Controllers\\ExportController@downloadUom", "middleware": ["web"], "before": "", "after": ""}], "notifications": [], "emailsData": [], "viewsData": [{"description": "Rendering a view", "start": 1754640293.442552, "end": 1754640293.442552, "duration": 0, "color": null, "data": {"name": "livewire.project.crm.index", "data": {"__type__": "array", "activeTab": null}}}, {"description": "Rendering a view", "start": 1754640293.449562, "end": 1754640293.449562, "duration": 0, "color": null, "data": {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "data": {"__type__": "array", "content": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;rD7c6DJj3FBY31chvKP6&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;777c1ccd8f755d1e0450349e7a989cd7116f82d4e1e44abcbd3cb0b5bf402edc&quot;}\" wire:effects=\"[]\" wire:id=\"rD7c6DJj3FBY31chvKP6\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>", "layout": {"__class__": "Livewire\\Features\\SupportPageComponents\\PageComponentConfig", "slots": {"__type__": "array"}, "viewContext": {"__class__": "Livewire\\Mechanisms\\HandleComponents\\ViewContext", "slots": {"__type__": "array"}, "pushes": {"__type__": "array"}, "prepends": {"__type__": "array"}, "sections": {"__type__": "array"}}, "response": null, "type": "component", "view": "layouts.app", "slotOrSection": "slot", "params": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}}}}}}, {"description": "Rendering a view", "start": 1754640293.452001, "end": 1754640293.452001, "duration": 0, "color": null, "data": {"name": "layouts.app", "data": {"__type__": "array", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;rD7c6DJj3FBY31chvKP6&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;777c1ccd8f755d1e0450349e7a989cd7116f82d4e1e44abcbd3cb0b5bf402edc&quot;}\" wire:effects=\"[]\" wire:id=\"rD7c6DJj3FBY31chvKP6\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}, {"description": "Rendering a view", "start": 1754640294.204711, "end": 1754640294.204711, "duration": 0, "color": null, "data": {"name": "livewire.component.sidebar", "data": {"__type__": "array", "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1754640294.206653, "end": 1754640294.206653, "duration": 0, "color": null, "data": {"name": "partials.sidebar.header", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "MvxBnJ0oeSZhrtdxaPnB", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}}}, {"description": "Rendering a view", "start": 1754640294.208597, "end": 1754640294.208597, "duration": 0, "color": null, "data": {"name": "partials.sidebar.user-menu", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "MvxBnJ0oeSZhrtdxaPnB", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}, "loop": null}}}, {"description": "Rendering a view", "start": 1754640294.210498, "end": 1754640294.210498, "duration": 0, "color": null, "data": {"name": "partials.sidebar.item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "MvxBnJ0oeSZhrtdxaPnB", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}, "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}}, {"description": "Rendering a view", "start": 1754640294.212114, "end": 1754640294.212114, "duration": 0, "color": null, "data": {"name": "partials.sidebar.simple-item", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array", "default": {"__class__": "Illuminate\\Support\\MessageBag", "*messages": {"__type__": "array"}, "*format": ":message"}}}, "_instance": {"__class__": "App\\Livewire\\Component\\Sidebar", "*__id": "MvxBnJ0oeSZhrtdxaPnB", "*__name": "component.sidebar", "*listeners": {"__type__": "array", "photo-updated": "refreshUserData"}, "*attributes": {"__class__": "Livewire\\Features\\SupportAttributes\\AttributeCollection", "*items": {"__type__": "array"}, "*escapeWhenCastingToString": false}, "*withValidatorCallback": null, "*rulesFromOutside": {"__type__": "array"}, "*messagesFromOutside": {"__type__": "array"}, "*validationAttributesFromOutside": {"__type__": "array"}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm"}, "menuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Home", "id": "home", "icon": "ki-home", "url": "http://localhost:8000/home", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "ePerolehan", "id": "eper<PERSON>han", "icon": "ki-document", "url": "http://localhost:8000/ep", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "section", "title": "Quotation Tender", "id": "qt", "url": "http://localhost:8000/ep?tab=qt", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Dashboard", "id": "qt-dashboard", "url": "http://localhost:8000/ep?tab=qt-dashboard", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Summary", "id": "summary", "url": "http://localhost:8000/ep?tab=qt-summary", "active": false}}}, "1": {"__type__": "array", "type": "section", "title": "Profile", "id": "profile", "url": "http://localhost:8000/ep?tab=profile", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "MyIdentity", "id": "my-identity", "url": "http://localhost:8000/ep?tab=my-identity", "active": false}, "1": {"__type__": "array", "type": "item", "title": "User Login", "id": "user-login", "url": "http://localhost:8000/ep?tab=user-login", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find Organization", "id": "find-organization", "url": "http://localhost:8000/ep?tab=organization", "active": false}}}, "2": {"__type__": "array", "type": "section", "title": "Supplier", "id": "supplier", "url": "http://localhost:8000/ep?tab=supplier", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-technical", "url": "http://localhost:8000/ep?tab=supplier-technical", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Supplier", "id": "find-supplier-cs", "url": "http://localhost:8000/ep?tab=supplier-cs", "active": false}}}, "3": {"__type__": "array", "type": "item", "title": "Tracking Diary", "id": "tracking-diary", "url": "http://localhost:8000/ep?tab=tracking-diary", "active": false}, "4": {"__type__": "array", "type": "section", "title": "<PERSON><PERSON>", "id": "item", "url": "http://localhost:8000/ep?tab=item", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find UOM", "id": "find-uom", "url": "http://localhost:8000/ep?tab=uom", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Find Items", "id": "find-items", "url": "http://localhost:8000/ep?tab=item-code", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Find UNSPSC Items", "id": "find-unspsc-items", "url": "http://localhost:8000/ep?tab=unspsc-item", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Find Supplier's Item", "id": "find-supplier-item", "url": "http://localhost:8000/ep?tab=supplier-item", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Find Item Task History", "id": "item-task-history", "url": "http://localhost:8000/ep?tab=item-history", "active": false}}}}}, "3": {"__type__": "array", "type": "item", "title": "Report - Revenue", "id": "report", "icon": "ki-graph-up", "url": "http://localhost:8000/report", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Today Transaction", "id": "today-trans", "url": "http://localhost:8000/report?tab=today-trans", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Accumulative YTD & MTD", "id": "accumulative", "url": "http://localhost:8000/report?tab=accumulative", "active": false}}}, "4": {"__type__": "array", "type": "item", "title": "BPM", "id": "bpm", "icon": "ki-wrench", "url": "http://localhost:8000/bpm", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Find Task", "id": "find-task", "url": "http://localhost:8000/bpm?tab=find-task", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Instance Query", "id": "instance-query", "url": "http://localhost:8000/bpm?tab=instance-query", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Process Manager", "id": "process-manager", "url": "http://localhost:8000/bpm?tab=process-manager", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Worklist Manager", "id": "worklist-manager", "url": "http://localhost:8000/bpm?tab=worklist-manager", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Service Manager", "id": "service-manager", "url": "http://localhost:8000/bpm?tab=service-manager", "active": false}, "5": {"__type__": "array", "type": "item", "title": "Track Comp. Instance", "id": "component-instance", "url": "http://localhost:8000/bpm?tab=component-instance", "active": false}}}, "5": {"__type__": "array", "type": "section", "title": "App Settings", "id": "settings", "icon": "ki-setting", "hidden": true, "url": "http://localhost:8000/settings?tab=users", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "Users", "id": "users", "url": "http://localhost:8000/settings?tab=users", "active": false}, "1": {"__type__": "array", "type": "item", "title": "Groups", "id": "groups", "url": "http://localhost:8000/settings?tab=groups", "active": false}, "2": {"__type__": "array", "type": "item", "title": "Roles", "id": "roles", "url": "http://localhost:8000/settings?tab=roles", "active": false}, "3": {"__type__": "array", "type": "item", "title": "Permissions", "id": "permissions", "url": "http://localhost:8000/settings?tab=permissions", "active": false}, "4": {"__type__": "array", "type": "item", "title": "Access Control List", "id": "acl", "url": "http://localhost:8000/settings?tab=access-control-list", "active": false}}}}, "secondaryMenuItems": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}}, "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "currentRoute": "http://localhost:8000/crm", "activeTopLevelItem": "crm", "menuItem": {"__type__": "array", "type": "item", "title": "CRM", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm", "active": true, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}}, "loop": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 2, "count": 3, "first": true, "last": false, "odd": true, "even": false, "depth": 2, "parent": {"__class__": "stdClass", "iteration": 1, "index": 0, "remaining": 0, "count": 1, "first": true, "last": true, "odd": true, "even": false, "depth": 1, "parent": null}}, "type": "item", "title": "CRM - CS Cases", "id": "crm", "icon": "ki-information", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false, "items": {"__type__": "array", "0": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "1": {"__type__": "array", "type": "item", "title": "CRM - CS Pending Input", "id": "cs-pending-input", "url": "http://localhost:8000/crm?tab=cs-pending-input", "active": false}, "2": {"__type__": "array", "type": "item", "title": "POMS - Talkdesk", "id": "poms-talkdesk", "url": "http://localhost:8000/crm?tab=poms-talkdesk", "active": false}}, "level": 2, "linkPadding": "ps-2.5", "levelClasses": "", "item": {"__type__": "array", "type": "item", "title": "CRM - CS Cases", "id": "cs-cases", "url": "http://localhost:8000/crm?tab=cs-cases", "active": false}, "disabled": false, "badge": null}}}, {"description": "Rendering a view", "start": 1754640294.225728, "end": 1754640294.225728, "duration": 0, "color": null, "data": {"name": "livewire.component.header", "data": {"__type__": "array", "user": {"__type__": "array", "first_name": "IQBAL FIKRI MOHAMED MISMAN", "last_name": ".", "name": "IQBAL FIKRI MOHAMED MISMAN .", "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "breadcrumbs": {"__type__": "array", "0": {"__type__": "array", "title": "CRM", "is_last": true, "route": "crm", "query": null}}}}}, {"description": "Rendering a view", "start": 1754640294.227952, "end": 1754640294.227952, "duration": 0, "color": null, "data": {"name": "components.footer", "data": {"__type__": "array", "app": {"__class__": "Illuminate\\Foundation\\Application", "__omitted__": "blackbox"}, "errors": {"__class__": "Illuminate\\Support\\ViewErrorBag", "*bags": {"__type__": "array"}}, "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "slot": {"__class__": "Illuminate\\View\\ComponentSlot", "attributes": {"__class__": "Illuminate\\View\\ComponentAttributeBag", "*attributes": {"__type__": "array"}}, "*contents": "<div wire:snapshot=\"{&quot;data&quot;:{&quot;activeTab&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;rD7c6DJj3FBY31chvKP6&quot;,&quot;name&quot;:&quot;project.c-r-m&quot;,&quot;path&quot;:&quot;crm&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;777c1ccd8f755d1e0450349e7a989cd7116f82d4e1e44abcbd3cb0b5bf402edc&quot;}\" wire:effects=\"[]\" wire:id=\"rD7c6DJj3FBY31chvKP6\">\n    <div class=\"d-flex flex-column flex-lg-row\">\n        <!-- Tabs navigation -->\n        <div class=\"d-flex flex-column flex-lg-row gap-5 gap-lg-0\">\n            <!-- Dynamic component container -->\n            <div class=\"tab-content flex-grow-1\">\n                <!--[if BLOCK]><![endif]--><!--[if ENDBLOCK]><![endif]-->\n            </div>\n        </div>\n    </div>\n</div>"}}}}], "userData": [], "httpRequests": [], "subrequests": [], "xdebug": [], "commandName": null, "commandArguments": [], "commandArgumentsDefaults": [], "commandOptions": [], "commandOptionsDefaults": [], "commandExitCode": null, "commandOutput": null, "jobName": null, "jobDescription": null, "jobStatus": null, "jobPayload": [], "jobQueue": null, "jobConnection": null, "jobOptions": [], "testName": null, "testStatus": null, "testStatusMessage": null, "testAsserts": [], "clientMetrics": [], "webVitals": [], "parent": null, "updateToken": "1996434f"}